import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:wd/core/constants/assets.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/utils/global_config.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/features/page/0_tiktok/video_home_view.dart';
import 'package:wd/features/page/0_tiktok/video_library/video_library_view.dart';
import 'package:wd/features/page/1_game_home/game_home_view.dart';
import 'package:wd/features/page/2_activity/activity_list_view.dart';
import 'package:wd/features/page/3_transact/transact_view.dart';
import 'package:wd/features/page/4_mine/mine_v3_view.dart';
import 'package:wd/features/page/5_debug_uikit/debug_uikit_page.dart';
import 'package:wd/injection_container.dart';

/// 底部导航栏页面类型
enum BottomNavType {
  gameHome, // 游戏首页
  videoHome, // 视频首页
  promotion, // 活动页
  agent, // 代理页
  mine, // 账户页
  debugUIKit, // 组件页
}

/// 底部导航栏页面配置
class BottomNavConfig {
  final BottomNavType type;
  final String title;
  final String normalIcon;
  final String selectIcon;
  final Size size;
  final Widget page;
  final bool isVisible;

  BottomNavConfig({
    required this.type,
    required this.title,
    required this.normalIcon,
    required this.selectIcon,
    required this.size,
    required this.page,
    this.isVisible = true,
  });

  /// 获取所有可见的页面配置
  static List<BottomNavConfig> getVisibleConfigs(context, {bool? tiktokTabVisible}) {
    final isVisible = tiktokTabVisible ?? sl<UserCubit>().state.userInfo?.tiktokTabVisible ?? true;
    return allConfigs(context).where((config) {
      if (!config.isVisible) return false;

      switch (config.type) {
        case BottomNavType.videoHome:
          return isVisible;
        default:
          return true;
      }
    }).toList();
  }

  /// 获取所有页面配置
  static List<BottomNavConfig> allConfigs(context) => [
        BottomNavConfig(
          type: BottomNavType.gameHome,
          title: tr("nav_game", context: context),
          normalIcon: Assets.gameIcon,
          selectIcon: Assets.gameSelectedIcon,
          size: Size(20.gw, 20.gw),
          page: const GameHomePage(),
          isVisible: true,
        ),
        BottomNavConfig(
          type: BottomNavType.videoHome,
          title: tr("nav_video", context: context),
          normalIcon: Assets.videoIcon,
          selectIcon: Assets.videoSelectedIcon,
          size: Size(20.gw, 18.5.gw),
          page: VideoLibraryPage(),
          isVisible: GlobalConfig.needShowVideoPage(),
        ),
        BottomNavConfig(
          type: BottomNavType.promotion,
          title: tr("nav_promotion", context: context),
          normalIcon: Assets.promotionIcon,
          selectIcon: Assets.promotionSelectedIcon,
          size: Size(18.5.gw, 20.gw),
          page: const ActivityListPage(),
          isVisible: true,
        ),
        BottomNavConfig(
          type: BottomNavType.agent,
          title: tr("nav_agent", context: context),
          normalIcon: Assets.agentIcon,
          selectIcon: Assets.agentIcon,
          size: Size(25.5.gw, 18.5.gw),
          page: const TransactPage(),
          // page: const MineV2Page(),
          isVisible: true,
        ),
        BottomNavConfig(
          type: BottomNavType.mine,
          title: tr("nav_account", context: context),
          normalIcon: Assets.profileIcon,
          selectIcon: Assets.profileSelectedIcon,
          size: Size(20.gw, 20.gw),
          page: const MineV3Page(),
          isVisible: true,
        ),
        BottomNavConfig(
          type: BottomNavType.debugUIKit,
          title: tr("UIKit", context: context),
          normalIcon: Assets.uikitIcon,
          selectIcon: Assets.uikitIcon,
          size: Size(20.gw, 20.gw),
          page: const DebugUikitPage(),
          isVisible: kDebugMode,
        ),
      ];

  /// 根据类型获取配置
  static BottomNavConfig? getConfigByType(context, {required BottomNavType type}) {
    return allConfigs(context).firstWhere((config) => config.type == type);
  }

  /// 获取页面索引
  static int getPageIndex(context, {required BottomNavType type}) {
    final visibleConfigs = getVisibleConfigs(context);
    return visibleConfigs.indexWhere((config) => config.type == type);
  }

  /// 获取页面总数
  static int getPageCount(context) {
    return getVisibleConfigs(context).length;
  }
}
