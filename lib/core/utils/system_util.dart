import 'dart:async';
import 'dart:io' show Platform;
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart' show PlatformDispatcher, Uint8List, kIsWeb;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:wd/core/constants/constants.dart';
import 'package:wd/core/models/apis/config.dart';
import 'package:wd/core/models/entities/jump_model.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/device_performance.dart';
import 'package:wd/core/utils/game_util.dart';
import 'package:wd/core/utils/global_config.dart';
import 'package:wd/core/utils/image_downloader/image_downloader.dart';
import 'package:wd/core/utils/log_util.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:wd/features/page/4_mine/account_security/modify_pwd/modify_pwd_view.dart';
import 'package:wd/features/routers/app_router.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/generated/firebase-options/firebase_options_wd.dart' as wd;
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/common_dialog.dart';
import 'package:wd/shared/widgets/dialog/app_update_dialog.dart';
import 'package:wd/shared/widgets/easy_loading.dart';
import 'package:open_file/open_file.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:wd/core/utils/platform/browser_detector_web.dart'
    if (dart.library.io) 'package:wd/core/utils/platform/browser_detector_non_web.dart';
import 'package:wd/core/utils/avif_support_checker/avif_support_checker.dart';

import '../models/entities/app_version_entity.dart';
import 'auth_util.dart';
import 'fingerprint/fingerprint_web.dart' if (dart.library.io) 'fingerprint/fingerprint_app.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';

import 'host_util.dart';

const kAppLocale = "kAppLocale";

class SystemUtil {
  static final SystemUtil _instance = SystemUtil._internal();
  factory SystemUtil() => _instance;
  SystemUtil._internal();

  static String version = '';
  static String buildNumber = '';
  static String? _deviceId;
  static bool isLowPerformanceDevice = false;
  static bool isFirstInstall = false; // 是否第一次安装
  static bool isAvifSupport = false; // 是否支持avif 默认false


  // 初始化时获取版本信息
  static Future<void> init() async {
    checkIsAvifSupport();
    await initAppDomain();
    initFirebase();
    final packageInfo = await PackageInfo.fromPlatform();
    version = packageInfo.version;
    buildNumber = packageInfo.buildNumber;
    // 初始化时获取设备ID
    _deviceId = await getFingerprint();
    isLowPerformanceDevice = await DevicePerformance().isLowPerformanceDevice();

    isFirstInstall = await _isFirstInstall();
  }

  static initAppDomain() async {
    /// FIXME
    return;
    if (kIsWeb) return;
    if (kDebug) return; // 后端oss域名列表失效时
    var host = await HostUtil().fetchHostFromOss();
    LogI("HostUtil选择域名>>> $host");
    if (host != null) {
      GlobalConfig().appBaseUrl = host;
    }
  }

  // 初始化Firebase 服务
  static initFirebase() async {
    if (kIsWeb) return;
    final firebaseOptions = switch (AppFlavor.fromChannel()) {
      AppFlavor.kWD => wd.DefaultFirebaseOptions.currentPlatform,
    };
    await Firebase.initializeApp(options: firebaseOptions);
    const fatalError = true;
    // Non-async exceptions
    FlutterError.onError = (errorDetails) {
      if (fatalError) {
        // If you want to record a "fatal" exception
        FirebaseCrashlytics.instance.recordFlutterFatalError(errorDetails);
        // ignore: dead_code
      } else {
        // If you want to record a "non-fatal" exception
        FirebaseCrashlytics.instance.recordFlutterError(errorDetails);
      }
    };
    // Async exceptions
    PlatformDispatcher.instance.onError = (error, stack) {
      if (fatalError) {
        // If you want to record a "fatal" exception
        FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
        // ignore: dead_code
      } else {
        // If you want to record a "non-fatal" exception
        FirebaseCrashlytics.instance.recordError(error, stack);
      }
      return true;
    };
  }

  // 新增 getter 方法
  static String get deviceId {
    if (_deviceId == null) {
      throw StateError('SystemUtil not initialized. Call SystemUtil.init() first.');
    }
    return _deviceId!;
  }

  static checkIsAvifSupport() async {
    isAvifSupport = await isAvifSupported();
    LogI("isAvifSupported: 是否支持AVIF >>> $isAvifSupport");
  }

  // 移动到私有方法
  // static Future<String> _getOrGenerateDeviceId() async {
  //   const deviceIdKey = 'device_uuid';
  //   final prefs = await SharedPreferences.getInstance();
  //
  //   String? deviceId = prefs.getString(deviceIdKey);
  //   if (deviceId != null) {
  //     return deviceId;
  //   }
  //   deviceId = await getFingerprint();
  //   // deviceId = await _generateDeviceId();
  //   await prefs.setString(deviceIdKey, deviceId);
  //   return deviceId;
  // }

  // static Future<String> _generateDeviceId() async {
  //   final deviceInfo = DeviceInfoPlugin();
  //   String? deviceIdentifier;
  //
  //   if (kIsWeb) {
  //     final webInfo = await deviceInfo.webBrowserInfo;
  //     deviceIdentifier = '${webInfo.browserName}-${webInfo.platform}-${webInfo.language}';
  //   } else if (Platform.isAndroid) {
  //     final androidInfo = await deviceInfo.androidInfo;
  //     int cpuCores = Platform.numberOfProcessors;
  //     deviceIdentifier = androidInfo.id;
  //   } else if (Platform.isIOS) {
  //     final iosInfo = await deviceInfo.iosInfo;
  //     deviceIdentifier = iosInfo.identifierForVendor;
  //   }
  //
  //   return const Uuid().v5(Uuid.NAMESPACE_URL, deviceIdentifier ?? DateTime.now().toString());
  // }

  // 获取平台
  static String getPlatform() {
    return detectBrowserOrPlatform();
  }

  // 获取版本号
  static String getVersion() {
    return version;
  }

  // 获取构建号
  static String getBuildNumber() {
    return buildNumber;
  }

  static String getSystemName() {
    return Platform.operatingSystem;
  }

  static bool isApp() {
    return isAndroid() || isIOS();
  }

  static bool isAndroid() {
    if (kIsWeb) {
      return false;
    }
    return Platform.isAndroid;
  }

  static bool isIOS() {
    if (kIsWeb) {
      return false;
    }
    return Platform.isIOS;
  }

  static bool isWeb() {
    return kIsWeb;
  }

  static bool isWebMobile() {
    if (!kIsWeb) return false;
    return getPlatform().toLowerCase().contains('mobile');
  }

  static bool isWebDesktop() {
    if (!kIsWeb) return false;
    return !isWebMobile();
  }

  static String getPlatformCode() {
    if (isWeb()) {
      return '3';
    } else if (isAndroid()) {
      return '1';
    } else if (isIOS()) {
      return '2';
    } else {
      return '0';
    }
  }

  static openCommonWebView({String? title, required String url}) {
    url = StringUtil.fixUrl(url);
    sl<NavigatorService>().push(AppRouter.commonWebView, arguments: {"title": title, "url": url});
  }

  /// 打开在线客服链接
  static contactService() async {
    GSEasyLoading.showLoading();
    final url = await GlobalConfig().getConfigValueByKey("service_url", forceUpdate: true);
    final isOpenByWebView = GlobalConfig().systemConfig!.serviceOpenType == '1'; // 1内跳 2外跳
    GSEasyLoading.dismiss();
    if (isOpenByWebView) {
      openCommonWebView(title: "在线客服", url: url!);
    } else {
      openUrlOnSystemBrowser(url: url!);
    }
  }

  static goToAppDownload() async {
    final url = await GlobalConfig().getConfigValueByKey("app_download_url", forceUpdate: true);
    openUrlOnSystemBrowser(url: "${url!}$kInviteUrlPath", needOpenNewTag: true);
    // sl<NavigatorService>().push(AppRouter.commonWebView, arguments: {"title": "App下载", "url": kAppDownloadLinkStr});
  }

  /// 使用系统浏览器访问url
  /// [url] 需访问的url
  /// [needOpenNewTag] 仅web有效，是否需要打开新标签，默认是false
  static openUrlOnSystemBrowser({required String url, bool needOpenNewTag = false}) async {
    try {
      // 1. URL预处理和检查
      url = StringUtil.fixUrl(url);

      final uri = Uri.tryParse(url);
      if (uri == null) {
        return GSEasyLoading.showToast("无效地址");
      }

      GSEasyLoading.showLoading();

      final canLaunch = await canLaunchUrl(uri);

      if (canLaunch) {
        // 4. 针对不同平台使用不同的打开模式
        if (isWeb()) {
          await launchUrl(uri, webOnlyWindowName: needOpenNewTag ? '_blank' : '_self');
        } else {
          // 5. iOS特殊处理
          await launchUrl(uri, mode: LaunchMode.externalApplication);
        }
      } else {
        throw 'Could not launch $uri';
      }

      GSEasyLoading.dismiss();
    } catch (e) {
      GSEasyLoading.showToast("打开浏览器失败 $e");
      GSEasyLoading.dismiss();
      rethrow; // 使用rethrow而不是throw Exception(e)以保留原始堆栈跟踪
    }
  }

  static Future<bool> _isFirstInstall() async {
    const firstInstallKey = 'kFirstInstallKey';
    final prefs = await SharedPreferences.getInstance();
    final isFirstInstall = prefs.getBool(firstInstallKey) ?? true;

    if (isFirstInstall) {
      // 如果是首次安装，则更新标记为 false
      await prefs.setBool(firstInstallKey, false);
    }

    return isFirstInstall;
  }

  // 检查app是否应该更新
  static checkAppUpdate() async {
    if (SystemUtil.isWeb() || GlobalConfig().isCheckAppUpdateDone) return false;
    try {
      final model = await ConfigApi.checkAppVersion();
      GlobalConfig().isCheckAppUpdateDone = true;
      if (model != null) {
        bool shouldShowUpdateDialog = await _shouldUpdate(model.version);
        // bool shouldShowUpdateDialog = await _shouldUpdate("1.2.2");
        if (shouldShowUpdateDialog) {
          AppUpdateDialog(model: model).show();
        }
      }
    } catch (e) {
      LogE("SystemUtil().checkAppUpdate.Error: $e");
    }
  }

  // 检查app是否应该更新
  static Future<bool> _shouldUpdate(String newVersion) async {
    // 检查 newVersion 格式是否符合 xx.xx.xx
    final versionRegex = RegExp(r'^\d+\.\d+\.\d+$');
    if (!versionRegex.hasMatch(newVersion)) {
      // 如果格式不对，返回 false
      return false;
    }

    // 获取本地应用信息
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    String currentVersion = packageInfo.version;
    print("当前版本号: $currentVersion, 最新版本号: $newVersion");

    // 将版本号按 "." 分割
    List<String> currentParts = currentVersion.split('.');
    List<String> newParts = newVersion.split('.');

    // 将每个部分转换成数字
    int currentMajor = int.parse(currentParts[0]);
    int currentMinor = int.parse(currentParts[1]);
    int currentPatch = int.parse(currentParts[2]);

    int newMajor = int.parse(newParts[0]);
    int newMinor = int.parse(newParts[1]);
    int newPatch = int.parse(newParts[2]);

    // 依次比较主版本号、次版本号和修订号
    if (newMajor > currentMajor) {
      return true;
    } else if (newMajor == currentMajor) {
      if (newMinor > currentMinor) {
        return true;
      } else if (newMinor == currentMinor) {
        if (newPatch > currentPatch) {
          return true;
        }
      }
    }
    // 如果新版本号不大于当前版本号，返回false
    return false;
  }

  static Future<void> saveImage(Uint8List pngBytes) async {
    ImageDownloader().saveImage(pngBytes);
  }

  /// 设置系统顶部底部颜色style
  static setSystemUIOverlayStyle(SystemUiOverlayStyle style) {
    final isDark = style == SystemUiOverlayStyle.dark;
    if (SystemUtil.isAndroid()) {
      SystemUiOverlayStyle systemUiOverlayStyle = SystemUiOverlayStyle(
        // statusBarColor: isDark ? AppTheme.instance.getThemeLight().appBarTheme.backgroundColor : AppTheme.instance.getThemeDark().appBarTheme.backgroundColor,
        // statusBarIconBrightness: isDark ? Brightness.light : Brightness.dark,
        // systemNavigationBarColor: isDark ? AppTheme.instance.getThemeLight().bottomNavigationBarTheme.backgroundColor : AppTheme.instance.getThemeDark().bottomNavigationBarTheme.backgroundColor,

        // 安卓状态栏背景色
        statusBarColor: AppTheme.instance.getThemeDark().appBarTheme.backgroundColor,
        // 安卓状态栏图标颜色
        statusBarIconBrightness: Brightness.light,
        // 安卓底部安全边距颜色
        systemNavigationBarColor: AppTheme.instance.getThemeDark().bottomNavigationBarTheme.backgroundColor,

      );
      SystemChrome.setSystemUIOverlayStyle(systemUiOverlayStyle);
    } else if (SystemUtil.isIOS()) {
      SystemChrome.setSystemUIOverlayStyle(
        SystemUiOverlayStyle(
          statusBarColor: isDark ? Colors.white : Colors.black,
          statusBarBrightness: isDark ? Brightness.dark : Brightness.light, // iOS 用，light = 黑字
        ),
      );
    }
  }

  static Future<bool> manageAppDownload({required String url, Function(int)? updateProgress}) async {
    if (SystemUtil.isIOS()) {
      return SystemUtil.openUrlOnSystemBrowser(url: url, needOpenNewTag: true);
    }
    if (!await requestInstallPermission()) throw Exception("无应用内安装apk权限");

    try {
      final appDocDir = await getTemporaryDirectory();
      final savePath = "${appDocDir.path}/app-${GlobalConfig.getAppName()}.apk";
      await ConfigApi.download(
        url: url,
        savePath: savePath,
        onProgress: updateProgress,
      );

      final res = await OpenFile.open(savePath);
      return res.type == ResultType.done;
    } catch (e) {
      rethrow;
    }
  }

  static Future<bool> requestInstallPermission() async {
    if (await Permission.requestInstallPackages.request().isGranted) {
      return true;
    }

    if (await Permission.requestInstallPackages.isPermanentlyDenied) {
      // 如果用户永久拒绝权限，跳转到系统设置
      openAppSettings();
    }
    return false;
  }

  static Future<(bool, AppVersionEntity?)> appUpdate() async {
    try {
      final model = await ConfigApi.checkAppVersion();
      if (model == null) return (false, null);
      final shouldShowUpdateDialog = await _shouldUpdate(model.version);
      return (shouldShowUpdateDialog, model);
    } catch (e) {
      LogE("checkAppUpdate.Error: $e");
      return (false, null);
    }
  }

  // unknown(-999, '未知类型'),
  // internalLink(10, '内部跳转链接'),
  // externalLink(12, '外部跳转链接'),
  // venueLink(13, '跳相关场馆'),
  // gameLink(233, '跳相关游戏'),
  // noJump(14, '不跳转');
  static onJump(JumpModel model) {
    switch (model.type) {
      case JumpType.internalLink: // 内部跳转链接
        AuthUtil.checkIfLogin(() {
          sl<NavigatorService>()
              .push(AppRouter.commonWebView, arguments: {"url": model.params, "title": model.extraData});
        });
        break;
      case JumpType.externalLink: // 外部跳转链接
        SystemUtil.openUrlOnSystemBrowser(url: model.params, needOpenNewTag: true);
        break;
      case JumpType.venueLink: // 跳相关场馆
        List<String> codeList = model.params.split('-');
        if (codeList.length != 2) return GSEasyLoading.showToast("跳转失败: 参数异常");
        GameUtil().onClickPlatformCellByGameType(gameTypeCode: codeList.first, platformCode: codeList.last);
        break;
      case JumpType.gameLink: // 跳相关游戏

        break;

      default:
        break;
    }
  }

  // 提示设置资金密码
  static Future showSetFundPwdDialog(BuildContext context) async {
    return await CommonDialog.show(
        context: context,
        title: 'common_tips'.tr(),
        content: 'fundPwd_tip'.tr(),
        sureBtnTitle: 'go_set'.tr(),
        complete: () {
          sl<NavigatorService>().push(AppRouter.userModifyPwd, arguments: SetPasswordType.setFundPwd);
        });
  }
}
