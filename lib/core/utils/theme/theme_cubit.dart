import 'package:bloc/bloc.dart';
import 'package:flutter/material.dart';
import 'package:wd/core/utils/theme/theme_state.dart';

class ThemeCubit extends Cubit<ThemeState> {
  ThemeCubit() : super(ThemeState(themeMode: ThemeMode.light));

  void changeTheme(ThemeMode mode) {
    emit(state.copyWith(themeMode: mode));
  }

  ThemeState? fromJson(Map<String, dynamic> json) {
    return ThemeState(
      themeMode: ThemeMode.values[json['themeMode'] as int],
    );
  }

  Map<String, dynamic>? toJson(ThemeState state) {
    return {
      'themeMode': state.themeMode.index,
    };
  }
}