
import 'package:flutter/material.dart';

/// 基于flutter_screenutil修改，因为我们自己做了最大屏幕大小的限制，flutter_screenutil无法满足需求
/// Modified based on flutter_screenutil, because we have limited the maximum screen size ourselves,
/// flutter_screenutil cannot meet the requirements
extension GSSizeExtension on num {
  /// 根据屏幕比例返回宽度
  /// Returns the width according to the screen ratio
  double get gw => GSScreenUtil().setWidth(this);

  /// 根据屏幕比例返回高度
  /// Returns the height according to the screen ratio
  double get gh => GSScreenUtil().setHeight(this);

  /// 返回屏幕宽度的比例
  /// Returns the ratio of the screen width
  double get gsw => GSScreenUtil().screenWidth * this;

  double get gswWeb => GSScreenUtil().originalWidth * this;

  /// 返回屏幕高度的比例
  /// Returns the ratio of the screen height
  double get gsh => GSScreenUtil().screenHeight * this;
}

class GSScreenUtil {
  // 单例实例
  static final GSScreenUtil _instance = GSScreenUtil._internal();

  late Size _uiSize;
  late bool _splitScreenMode;
  late double maxWidth;

  double _screenWidth = 0;
  double _screenHeight = 0;

  // 工厂构造函数，返回单例实例
  factory GSScreenUtil() {
    return _instance;
  }

  // 私有构造函数，用于单例初始化
  GSScreenUtil._internal();

  bool isInitialized = false;

  // 初始化函数，传入 BuildContext、UI 设计尺寸、最大宽度等
  void init({
    required BuildContext context,
    required Size uiSize,
    bool splitScreenMode = false,
    double maxWidth = 475,
  }) {
    if (!isInitialized || MediaQuery.of(context).size.width < MediaQuery.of(context).size.height) {
      _screenWidth = MediaQuery.of(context).size.width;
      _screenHeight = MediaQuery.of(context).size.height;
      _uiSize = uiSize;
      _splitScreenMode = splitScreenMode;
      this.maxWidth = maxWidth;
      isInitialized = true;
    }
  }

  // 获取屏幕宽度
  double get screenWidth {
    return _screenWidth <= maxWidth ? _screenWidth : maxWidth;
  }

  // 获取原始屏幕宽度
  double get originalWidth => _screenWidth;

  // 获取屏幕高度
  double get screenHeight {
    return _screenHeight;
  }

  // 比例计算 - 实际宽度与设计稿宽度的比例
  double get scaleWidth => !_enableScaleWH() ? 1 : screenWidth / _uiSize.width;

  // 比例计算 - 实际高度与设计稿高度的比例
  double get scaleHeight {
    double calculatedHeight = screenHeight;
    return !_enableScaleWH() ? 1 : calculatedHeight / _uiSize.height;
  }

  // 判断是否启用宽高缩放比例
  bool _enableScaleWH() {
    return true;
  }

  // 设置宽度
  double setWidth(num width) {
    return width * scaleWidth;
  }

  // 设置高度
  double setHeight(num height) {
    return height * scaleHeight;
  }
}
