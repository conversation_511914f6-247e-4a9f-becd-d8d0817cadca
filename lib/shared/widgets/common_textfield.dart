import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/screenUtil.dart';

class CommonTextField extends StatefulWidget {
  final TextEditingController? controller;
  final TextStyle? textStyle;
  final String hintText;
  final Color? hintTextColor;
  final bool inputEnable;
  final bool? obscureText;
  final int? maxLength;
  final Color? fillColor;
  final TextInputType keyboardType;
  final bool isShowRequiredRedPoint;
  final double radius;
  final double? maxHeight;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final EdgeInsets? prefixIconPadding;
  final EdgeInsets? suffixIconPadding;
  final EdgeInsets? contentPadding;
  final FocusNode? focusNode;
  final ValueChanged<String>? onChanged;
  final ValueChanged<String>? onSubmitted;
  final List<TextInputFormatter>? inputFormatters;
  final Color? enabledBorderColor;
  final bool autofocus;
  final bool isUnderline;
  final bool isShowBorder;

  const CommonTextField({
    super.key,
    this.controller,
    this.textStyle,
    required this.hintText,
    this.hintTextColor,
    this.inputEnable = true,
    this.isShowRequiredRedPoint = false,
    this.radius = 10.0,
    this.maxHeight,
    this.obscureText,
    this.maxLength,
    this.fillColor,
    this.keyboardType = TextInputType.text,
    this.prefixIcon,
    this.prefixIconPadding,
    this.suffixIcon,
    this.suffixIconPadding,
    this.contentPadding,
    this.focusNode,
    this.onChanged,
    this.onSubmitted,
    this.inputFormatters,
    this.enabledBorderColor,
    this.autofocus = false,
    this.isUnderline = false,
    this.isShowBorder = false,
  });

  @override
  State<CommonTextField> createState() => _CommonTextFieldState();
}

class _CommonTextFieldState extends State<CommonTextField> {
  late TextEditingController _controller;
  bool _isShowCleanBtn = false;

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? TextEditingController();
    _isShowCleanBtn = _controller.text.isNotEmpty;

    _controller.addListener(() {
      final isNotEmpty = _controller.text.isNotEmpty;
      if (_isShowCleanBtn != isNotEmpty) {
        setState(() {
          _isShowCleanBtn = isNotEmpty;
        });
      }
    });
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ConstrainedBox(
      constraints: BoxConstraints(maxHeight: widget.maxHeight ?? 60.gw),

      child: TextField(
        controller: _controller,
        enabled: widget.inputEnable,
        maxLength: widget.maxLength,
        focusNode: widget.focusNode,
        autofocus: widget.autofocus,
        inputFormatters: widget.inputFormatters,
        keyboardType: widget.keyboardType,
        obscureText: widget.obscureText ?? false,
        textAlignVertical: TextAlignVertical.center,
        style: widget.textStyle ?? context.textTheme.secondary.fs16,
        onChanged: (text) => widget.onChanged?.call(text),
        onSubmitted: widget.onSubmitted,
        decoration: InputDecoration(
          filled: true,
          fillColor: widget.fillColor ?? context.theme.cardColor,
          contentPadding: widget.contentPadding ?? EdgeInsets.fromLTRB(8.gw, 0, 8.gw, 0),
          prefixIconConstraints: BoxConstraints(maxHeight: widget.maxHeight ?? 60.gw),
          prefixIcon: (widget.isShowRequiredRedPoint == true || widget.prefixIcon != null)
              ? Padding(
                padding: widget.prefixIconPadding ?? EdgeInsets.only(left: 16.gw, right: 8.gw),
                child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (widget.isShowRequiredRedPoint)
                        Text(' * ', style: TextStyle(fontSize: 20.fs, color: Colors.red)),
                      if (widget.prefixIcon != null) widget.prefixIcon!,
                    ],
                  ),
              )
              : null,
          counterText: '',
          suffixIcon: Container(
            padding: widget.suffixIcon != null ? widget.suffixIconPadding : EdgeInsets.only(right: 15.gw),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                if (_isShowCleanBtn && widget.inputEnable)
                  IconButton(
                      icon: Image.asset(
                        'assets/images/common/icon_textField_clear.png',
                        width: 20.gw,
                        height: 20.gw,
                      ),
                      onPressed: () {
                        _controller.clear();
                        widget.onChanged?.call('');
                      }),
                if (widget.suffixIcon != null) widget.suffixIcon!,
              ],
            ),
          ),
          hintText: widget.hintText,
          hintStyle: TextStyle(
            color: widget.hintTextColor ?? Theme.of(context).inputDecorationTheme.hintStyle?.color,
            fontSize: 14.fs,
          ),
          alignLabelWithHint: true,
          border: _getBorder(),
          focusedBorder: _getFocusedBorder(),
          disabledBorder: _getDisabledBorder(),
          enabledBorder: _getEnabledBorder(),
        ),
      ),
    );
  }

  OutlineInputBorder _buildOutlineBorder({Color color = Colors.transparent}) {
    return OutlineInputBorder(
      borderSide: BorderSide(color: widget.isShowBorder ? color : context.theme.cardColor, width: widget.isShowBorder ? 0.5 : 0),
      borderRadius: BorderRadius.circular(widget.radius),
    );
  }

  UnderlineInputBorder _buildUnderlineBorder({Color color = Colors.transparent}) {
    return UnderlineInputBorder(
      borderSide: BorderSide(color: color, width: widget.isShowBorder ? 0.5 : 0),
    );
  }

  InputBorder? _getBorder() {
    return null; // 使用默认边框
  }

  InputBorder _getFocusedBorder() {
    if (widget.isUnderline) {
      return _buildUnderlineBorder(color: Theme.of(context).primaryColor);
    }
    return _buildOutlineBorder(color: Theme.of(context).primaryColor);
  }

  InputBorder _getDisabledBorder() {
    if (widget.isUnderline) {
      return _buildUnderlineBorder(color: Theme.of(context).dividerColor);
    }
    return _buildOutlineBorder(color: Theme.of(context).dividerColor);
  }

  InputBorder _getEnabledBorder() {
    if (widget.isUnderline) {
      return _buildUnderlineBorder(color: widget.enabledBorderColor ?? const Color(0xffE0E0E0));
    }
    return _buildOutlineBorder(color: widget.enabledBorderColor ?? const Color(0xffE0E0E0));
  }
}
