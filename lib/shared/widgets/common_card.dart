import 'package:flutter/material.dart';
import 'package:wd/core/theme/themes.dart';

class CommonCard extends StatelessWidget {
  const CommonCard({super.key, required this.child});

  final Widget child;

  @override
  Widget build(BuildContext context) {
    return Card(
      color: context.theme.cardColor,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: child,
      ),
    );
  }
}
