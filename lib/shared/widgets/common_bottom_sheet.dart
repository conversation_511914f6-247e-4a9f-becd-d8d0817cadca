import 'package:flutter/material.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/common_button.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';

/// Configuration class for bottom sheet buttons
///
/// This class defines the properties for buttons that appear at the bottom
/// of the custom bottom sheet.
class BottomSheetButton {
  /// Button text/title
  final String title;

  /// Button press callback
  final VoidCallback? onPressed;

  /// Button style from CommonButtonStyle enum
  final CommonButtonStyle style;

  /// Custom background color (overrides style)
  final Color? backgroundColor;

  /// Custom text color (overrides style)
  final Color? textColor;

  /// Custom border color (overrides style)
  final Color? borderColor;

  /// Button width flex (for Row distribution)
  final int flex;

  /// Whether button is enabled
  final bool enabled;

  /// Whether to show loading state
  final bool showLoading;

  const BottomSheetButton({
    required this.title,
    this.onPressed,
    this.style = CommonButtonStyle.primary,
    this.backgroundColor,
    this.textColor,
    this.borderColor,
    this.flex = 1,
    this.enabled = true,
    this.showLoading = false,
  });
}

/// Configuration class for bottom sheet header
///
/// This class defines the properties for the header section of the bottom sheet.
class BottomSheetHeader {
  /// Main title text
  final String? title;

  /// Subtitle text (appears below title)
  final String? subtitle;

  /// Custom header widget (overrides title/subtitle)
  final Widget? customWidget;

  /// Whether to show close button
  final bool showCloseButton;

  /// Custom close button widget
  final Widget? customCloseButton;

  /// Close button callback
  final VoidCallback? onClose;

  /// Header background color
  final Color? backgroundColor;

  /// Header height
  final double? height;

  /// Header padding
  final EdgeInsets? padding;

  const BottomSheetHeader({
    this.title,
    this.subtitle,
    this.customWidget,
    this.showCloseButton = true,
    this.customCloseButton,
    this.onClose,
    this.backgroundColor,
    this.height,
    this.padding,
  });
}

/// A customizable bottom sheet component
///
/// This widget provides a flexible bottom sheet with customizable header,
/// content area, and bottom buttons. It follows the app's design system
/// and theme patterns.
///
/// Example usage:
/// ```dart
/// CommonBottomSheet.show(
///   context: context,
///   header: BottomSheetHeader(
///     title: 'Select Option',
///     subtitle: 'Choose one of the following options',
///   ),
///   children: [
///     ListTile(title: Text('Option 1')),
///     ListTile(title: Text('Option 2')),
///   ],
///   buttons: [
///     BottomSheetButton(
///       title: 'cancel'.tr(),
///       style: CommonButtonStyle.secondary,
///       onPressed: () => Navigator.pop(context),
///     ),
///     BottomSheetButton(
///       title: 'confirm'.tr(),
///       style: CommonButtonStyle.primary,
///       onPressed: () {
///         // Handle confirmation
///         Navigator.pop(context);
///       },
///     ),
///   ],
/// );
/// ```
class CommonBottomSheet extends StatelessWidget {
  /// Header configuration
  final BottomSheetHeader? header;

  /// List of widgets to display in the content area
  final List<Widget> children;

  /// List of buttons to display at the bottom
  final List<BottomSheetButton>? buttons;

  /// Whether the content area should be scrollable
  final bool isScrollable;

  /// Maximum height of the bottom sheet
  final double? maxHeight;

  /// Minimum height of the bottom sheet
  final double? minHeight;

  /// Background color of the entire bottom sheet
  final Color? backgroundColor;

  /// Border radius for the bottom sheet
  final double borderRadius;

  /// Content padding
  final EdgeInsets? contentPadding;

  /// Space between buttons
  final double buttonSpacing;

  /// Whether to add safe area padding at the bottom
  final bool addBottomSafeArea;

  /// Whether to show gradient overlay on scrollable content
  final bool showGradientOverlay;

  /// Height of the gradient overlay
  final double gradientOverlayHeight;

  /// Colors for the gradient overlay
  final List<Color>? gradientOverlayColors;

  /// Stops for the gradient overlay
  final List<double>? gradientOverlayStops;

  /// Whether the bottom sheet can be dismissed by tapping outside
  final bool isDismissible;

  const CommonBottomSheet({
    super.key,
    this.header,
    required this.children,
    this.buttons,
    this.isScrollable = true,
    this.maxHeight,
    this.minHeight,
    this.backgroundColor,
    this.borderRadius = 16.0,
    this.contentPadding,
    this.buttonSpacing = 16.0,
    this.addBottomSafeArea = true,
    this.showGradientOverlay = true,
    this.gradientOverlayHeight = 30.0,
    this.gradientOverlayColors,
    this.gradientOverlayStops,
    this.isDismissible = true,
  });

  /// Static method to show the bottom sheet
  ///
  /// This is the recommended way to display the custom bottom sheet.
  /// It handles all the necessary configuration for showModalBottomSheet.
  static Future<T?> show<T>({
    required BuildContext context,
    BottomSheetHeader? header,
    required List<Widget> children,
    List<BottomSheetButton>? buttons,
    bool isScrollable = true,
    double? maxHeight,
    double? minHeight,
    Color? backgroundColor,
    double borderRadius = 16.0,
    EdgeInsets? contentPadding,
    double buttonSpacing = 16.0,
    bool addBottomSafeArea = true,
    bool showGradientOverlay = false,
    double gradientOverlayHeight = 60.0,
    List<Color>? gradientOverlayColors,
    List<double>? gradientOverlayStops,
    bool isDismissible = true,
    bool enableDrag = true,
    bool isScrollControlled = true,
    Color? barrierColor,
  }) {
    return showModalBottomSheet<T>(
      context: context,
      backgroundColor: Colors.transparent,
      isDismissible: isDismissible,
      enableDrag: enableDrag,
      isScrollControlled: isScrollControlled,
      barrierColor: barrierColor,
      builder: (BuildContext context) {
        return CommonBottomSheet(
          header: header,
          buttons: buttons,
          isScrollable: isScrollable,
          maxHeight: maxHeight,
          minHeight: minHeight,
          backgroundColor: backgroundColor,
          borderRadius: borderRadius,
          contentPadding: contentPadding,
          buttonSpacing: buttonSpacing,
          addBottomSafeArea: addBottomSafeArea,
          showGradientOverlay: showGradientOverlay,
          gradientOverlayHeight: gradientOverlayHeight,
          gradientOverlayColors: gradientOverlayColors,
          gradientOverlayStops: gradientOverlayStops,
          isDismissible: isDismissible,
          children: children,
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final defaultMaxHeight = screenHeight * 0.9;
    final effectiveMaxHeight = maxHeight ?? defaultMaxHeight;
    final effectiveMinHeight = minHeight ?? 200.gw;

    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: isDismissible
          ? () {
              // Allow tapping outside to dismiss only if isDismissible is true
              Navigator.of(context).pop();
            }
          : null,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 20.gw),
            child: ConstrainedBox(
              constraints: BoxConstraints(
                maxHeight: effectiveMaxHeight,
                minHeight: effectiveMinHeight,
              ),
              child: Container(
                decoration: BoxDecoration(
                  color: backgroundColor ?? context.colorTheme.foregroundColor,
                  borderRadius: BorderRadius.circular(borderRadius.gw),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (header != null) _buildHeader(context),
                    _buildContent(context),
                    if (buttons != null && buttons!.isNotEmpty)
                      _buildBottomButtons(context),
                    if (addBottomSafeArea) _buildBottomSafeArea(context),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Builds the header section
  Widget _buildHeader(BuildContext context) {
    final headerConfig = header!;

    if (headerConfig.customWidget != null) {
      return headerConfig.customWidget!;
    }

    return Container(
      height: headerConfig.height ?? 61.gw,
      decoration: BoxDecoration(
        color:
            headerConfig.backgroundColor ?? context.colorTheme.foregroundColor,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(borderRadius.gw),
          topRight: Radius.circular(borderRadius.gw),
        ),
      ),
      padding: headerConfig.padding ?? EdgeInsets.symmetric(horizontal: 16.gw),
      child: Row(
        children: [
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                if (headerConfig.title != null)
                  AneText(
                    headerConfig.title!,
                    style: context.textTheme.secondary.fs20.w500,
                  ),
                if (headerConfig.subtitle != null) ...[
                  SizedBox(height: 4.gw),
                  AneText(
                    headerConfig.subtitle!,
                    style: context.textTheme.secondary.copyWith(
                      color: context.colorTheme.textSecondary,
                    ),
                  ),
                ],
              ],
            ),
          ),
          if (headerConfig.showCloseButton)
            headerConfig.customCloseButton ??
                IconButton(
                  onPressed:
                      headerConfig.onClose ?? () => Navigator.pop(context),
                  icon: Icon(
                    Icons.close,
                    color: context.colorTheme.textSecondary,
                    size: 24.gw,
                  ),
                ),
        ],
      ),
    );
  }

  /// Builds the content section
  Widget _buildContent(BuildContext context) {
    if (isScrollable) {
      if (showGradientOverlay) {
        return Expanded(
          child: ShaderMask(
            shaderCallback: (Rect bounds) {
              return LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.white, // Content fully visible at top
                  Colors.white, // Content fully visible
                  const Color(0xFF101010).withOpacity(1.0), // 100% #101010
                  const Color(0xFF101010).withOpacity(0.8), // 80% #101010
                  const Color(0xFF101010).withOpacity(0.4788), // 47.88% #101010
                  const Color(0xFF101010)
                      .withOpacity(0.0), // 0% #101010 (transparent)
                ],
                stops: const [0.0, 0.6, 0.7, 0.8, 0.9, 1.0],
              ).createShader(bounds);
            },
            blendMode: BlendMode.dstIn,
            child: GestureDetector(
              behavior: HitTestBehavior.deferToChild,
              child: SingleChildScrollView(
                padding: contentPadding ?? EdgeInsets.all(16.gw),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: children,
                ),
              ),
            ),
          ),
        );
      } else {
        return Flexible(
          child: SingleChildScrollView(
            child: Padding(
              padding: contentPadding ?? EdgeInsets.all(16.gw),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: children,
              ),
            ),
          ),
        );
      }
    }

    return Padding(
      padding: contentPadding ?? EdgeInsets.all(16.gw),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: children,
      ),
    );
  }

  /// Builds the bottom buttons section
  Widget _buildBottomButtons(BuildContext context) {
    if (buttons == null || buttons!.isEmpty) return const SizedBox.shrink();

    return Container(
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(borderRadius.gw),
          bottomRight: Radius.circular(borderRadius.gw),
        ),
      ),
      padding: EdgeInsets.all(16.gw),
      child: Row(
        children: _buildButtonList(context),
      ),
    );
  }

  /// Builds the list of buttons with proper spacing
  List<Widget> _buildButtonList(BuildContext context) {
    final List<Widget> buttonWidgets = [];

    for (int i = 0; i < buttons!.length; i++) {
      final button = buttons![i];

      buttonWidgets.add(
        Expanded(
          flex: button.flex,
          child: CommonButton(
            height: 47.gw,
            title: button.title,
            style: button.style,
            backgroundColor: button.backgroundColor,
            textColor: button.textColor,
            borderColor: button.borderColor,
            radius: 12.gw,
            enable: button.enabled,
            showLoading: button.showLoading,
            onPressed: button.onPressed,
          ),
        ),
      );

      // Add spacing between buttons (except for the last one)
      if (i < buttons!.length - 1) {
        buttonWidgets.add(SizedBox(width: buttonSpacing.gw));
      }
    }

    return buttonWidgets;
  }

  /// Builds the bottom safe area
  Widget _buildBottomSafeArea(BuildContext context) {
    final bottomPadding = MediaQuery.of(context).padding.bottom;
    return SizedBox(
      height: bottomPadding > 0 ? bottomPadding : 10.gw,
    );
  }
}
