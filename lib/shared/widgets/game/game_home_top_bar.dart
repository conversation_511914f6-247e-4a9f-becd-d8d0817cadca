import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:wd/core/constants/assets.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/singletons/user_state.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/auth_util.dart';
import 'package:wd/core/utils/game_util.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/utils/system_util.dart';
import 'package:wd/features/routers/app_router.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/home/<USER>';
import 'package:wd/shared/widgets/notification/notification_badge.dart';

import '../home/<USER>';

/// 游戏首页 顶部appbar
class GameHomeTopBar extends StatelessWidget {
  final GlobalKey<ScaffoldState> scaffoldKey;

  const GameHomeTopBar({
    super.key,
    required this.scaffoldKey,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 15.gw),
      height: 44.gw,
      color: context.theme.appBarTheme.backgroundColor,
      child: Row(
        children: [
          // Logo 图片
          InkWell(
              onTap: () => scaffoldKey.currentState?.openDrawer(),
              child: SvgPicture.asset(Assets.tabLogo, width: 68.gw, height: 31.gw,)),
          const Spacer(),

          // 判断是否已登录，显示余额信息或登录操作按钮
          BlocSelector<UserCubit, UserState, bool>(
              selector: (state) => state.isLogin,
              builder: (context, isLogin) {
                return isLogin ? _buildSideButton() : const HomeLoginOperateWidget();
              }),
        ],
      ),
    );
  }

  // 余额信息组件
  Widget _buildSideButton() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
          InkWell(
            onTap: () => SystemUtil.contactService(),
            child: Image.asset(Assets.iconSupport, width: 36.gw, height: 36.gw,),
          ),
          SizedBox(width: 12.gw),
          InkWell(
            onTap: () {
              AuthUtil.checkIfLogin(() {
                sl<NavigatorService>().push(AppRouter.notifications);
              });
            },
            child: NotificationBadge(
              child: Image.asset(Assets.iconAppBarNotice, width: 36.gw, height: 36.gw,),
            ),
          ),

      ],
    );
    return InkWell(
      onTap: () => scaffoldKey.currentState?.openDrawer(),
      child: NotificationBadge(
        child: Image.asset(
          "assets/images/home/<USER>",
          width: 20.gw,
          height: 20.gw,
        ),
      ),
    );
  }
}
