import 'package:flutter/material.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/app_image.dart';

enum CommonTabBarStyle {
  primary,
  secondary,
}

class CommonTabBarItem {
  final String title;
  final String? imageUrl;

  CommonTabBarItem({required this.title, this.imageUrl});
}

class CommonTabBar extends StatefulWidget {
  final List<CommonTabBarItem> data;
  final CommonTabBarStyle? style;
  final ValueChanged<int>? onTap;
  final int currentIndex;
  final bool showEndMark;
  final EdgeInsetsGeometry? tabPadding;

  // NEW: Whether the tab bar container should shrink to fit tab items
  final bool shrinkWrap;

  const CommonTabBar(
    this.data, {
    super.key,
    this.style = CommonTabBarStyle.primary,
    this.onTap,
    this.currentIndex = 0,
    this.showEndMark = false,
    this.tabPadding,
    this.shrinkWrap = false,
  });

  @override
  State<StatefulWidget> createState() => _CommonTabBarState();
}

class _CommonTabBarState extends State<CommonTabBar> with SingleTickerProviderStateMixin {
  late TabController _tabController;

  late final _height = 40.gw;
  late final _tabBarHeight = 32.gw;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(
      length: widget.data.length,
      vsync: this,
      initialIndex: widget.currentIndex,
    );
    _tabController.addListener(_handleTabChange);
  }

  @override
  void didUpdateWidget(covariant CommonTabBar old) {
    super.didUpdateWidget(old);

    // 1. 如果 data 长度变化，重新创建 controller
    if (old.data.length != widget.data.length) {
      _tabController
        ..removeListener(_handleTabChange)
        ..dispose();
      _tabController = TabController(
        length: widget.data.length,
        vsync: this,
        initialIndex: widget.currentIndex.clamp(0, widget.data.length - 1),
      )..addListener(_handleTabChange);
    }

    // 2. 如果外部 currentIndex 变化，同步到 TabController
    if (old.currentIndex != widget.currentIndex && widget.currentIndex != _tabController.index) {
      _tabController.animateTo(widget.currentIndex);
    }
  }

  void _handleTabChange() {
    // 只有用户手指拖动/点击才触发，避免外部 animateTo 再反吹
    if (_tabController.indexIsChanging) {
      widget.onTap?.call(_tabController.index);
      setState(() {});
    }
  }

  @override
  void dispose() {
    _tabController
      ..removeListener(_handleTabChange)
      ..dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          height: _height,
          decoration: BoxDecoration(
            color: context.theme.cardColor,
            borderRadius: BorderRadius.circular(12.gw),
          ),
          padding: EdgeInsets.symmetric(horizontal: 4.gw),
          alignment: Alignment.center,

          // NEW: shrinkWrap logic added here
          child: widget.shrinkWrap
              ? Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    IntrinsicWidth(
                      child: SizedBox(
                        height: _tabBarHeight,
                        child: _buildTabBar(context),
                      ),
                    ),
                  ],
                )
              : SizedBox(
                  height: _tabBarHeight,
                  width: double.infinity,
                  child: _buildTabBar(context),
                ),
        ),

        // 渐变遮罩右侧标记
        if (widget.showEndMark && _tabController.index != widget.data.length - 1)
          Positioned(
            top: 0,
            right: 0,
            child: IgnorePointer(
              child: Container(
                height: _height,
                width: 110.gw,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12.gw),
                  gradient: const LinearGradient(
                    begin: Alignment.centerRight,
                    end: Alignment.centerLeft,
                    colors: [
                      Color(0xFF101010), // #101010
                      Color(0x00101010), // rgba(16,16,16,0)
                    ],
                  ),
                ),
              ),
            ),
          ),
      ],
    );
  }

  // 提取 TabBar 构建逻辑，避免重复代码
  TabBar _buildTabBar(BuildContext context) {
    return TabBar(
      padding: EdgeInsets.zero,
      isScrollable: true,
      tabAlignment: TabAlignment.start,
      controller: _tabController,
      splashFactory: NoSplash.splashFactory,
      overlayColor: WidgetStateProperty.all(context.theme.cardColor),
      labelPadding: EdgeInsets.symmetric(horizontal: 3.gw),
      labelStyle: context.textTheme.primary,
      labelColor: context.theme.primaryColor,
      unselectedLabelStyle: context.textTheme.title,
      unselectedLabelColor: context.colorTheme.textTitle,
      indicator: BoxDecoration(
        color: context.colorTheme.tabItemBgA,
        borderRadius: BorderRadius.circular(10.gw),
        border: Border.all(color: context.colorTheme.borderE, width: 1),
      ),
      indicatorWeight: 0,
      indicatorSize: TabBarIndicatorSize.tab,
      tabs: [
        for (int i = 0; i < widget.data.length; i++)
          _buildTabItem(
            widget.data[i],
            isSel: _tabController.index == i,
          ),
      ],
    );
  }

  Widget _buildTabItem(CommonTabBarItem item, {bool isSel = false}) {
    // 仅在 secondary 且未选中时显示背景
    bool showBgDecoration = widget.style == CommonTabBarStyle.secondary && !isSel;

    return Tab(
      child: Container(
        height: _tabBarHeight,
        padding: widget.tabPadding ?? EdgeInsets.symmetric(horizontal: 11.gw),
        decoration: showBgDecoration
            ? BoxDecoration(
                color: context.colorTheme.borderA,
                borderRadius: BorderRadius.circular(10.gw),
              )
            : null,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (item.imageUrl != null) ...[
              AppImage(
                imageUrl: item.imageUrl!,
                height: 16.gw,
              ),
              SizedBox(width: 6.gw),
            ],
            Text(item.title),
          ],
        ),
      ),
    );
  }
}
