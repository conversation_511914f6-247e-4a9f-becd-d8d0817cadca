import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:wd/core/base/base_stateful_page.dart';
import 'package:wd/core/theme/custom_text_theme.dart';
import 'package:wd/core/theme/themes.dart';

import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/system_util.dart';
import 'package:wd/injection_container.dart';

class CommonAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String pageTitle;
  final Color? pageTitleColor;
  final Color? navColor;
  final String? leftImagePath;
  final Widget? leftWidget;
  final Widget? rightWidget;

  const CommonAppBar({
    super.key,
    required this.pageTitle,
    this.pageTitleColor,
    this.navColor,
    this.leftImagePath,
    this.leftWidget,
    this.rightWidget,
  });

  @override
  Widget build(BuildContext context) {
    final brightness = MediaQuery.of(context).platformBrightness;
    return AppBar(
      centerTitle: true,
      title: Text(
        pageTitle,
        style:context.theme.appBarTheme.titleTextStyle?.fs20,
      ),
      leading: leftWidget ??
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: Image(
              image: AssetImage(leftImagePath ?? "assets/images/toolBar/icon_toolBar_back.png"),
              height: 30,
              width: 30,
            ),
          ),
      elevation: 0.2,
      actions: [rightWidget ?? Container()],
      backgroundColor: navColor ?? Theme.of(context).appBarTheme.backgroundColor,
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
