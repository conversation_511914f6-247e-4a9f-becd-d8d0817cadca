import 'package:flutter/material.dart';
import 'package:path/path.dart';
import 'package:wd/core/models/entities/video_list_entity.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/shared/widgets/app_image.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';

class HomeVideoCell extends StatelessWidget {
  final VideoListRecords model;

  const HomeVideoCell({
    super.key,
    required this.model,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        image: DecorationImage(
          image: AssetImage("assets/images/home/<USER>"),
          fit: BoxFit.fill,)
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // 图片
          _getImageContainer(context),
          // 标题
          Expanded(
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 12.gw),
              alignment: Alignment.centerLeft,
              child: AneText(
                model.videoTitle,
                overflow: TextOverflow.ellipsis,
                style: context.textTheme.secondary.fs16,
                maxLines: 1,
              ),
            ),
          ),
        ],
      ),
    );
  }

  _getImageContainer(BuildContext context) {
    return Stack(
      children: [
        Container(
          margin: EdgeInsets.fromLTRB(4.gw, 4.gw, 4.gw, 0),
          width: double.infinity,
          clipBehavior: Clip.none,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(6),
          ),
          child: Stack(
            children: [
              AspectRatio(
                aspectRatio: 184/133,
                child: AppImage(
                   fit: BoxFit.cover,
                  imageUrl: model.videoImage,
                  placeholder: Container(color: context.theme.dividerColor),
                ),
              ),
              // 清晰度
              Positioned(right: 6.gw, top: 6.gw, child: _getBackgroundColorText(context, model.videoClarity)),
              // 时长
              Positioned(right: 6.gw, bottom: 6.gw, child: _getBackgroundColorText(context, model.videoTime, prefixIconPath: "assets/images/tiktok/icon_video_time.png")),
            ],
          ),
        ),

        // 热门
        Positioned(
            left:0,
            top: 0,
            child: Image.asset(
              "assets/images/tiktok/icon_video_hot.png",
              width: 59.gw,
              height: 45.gw,
              fit: BoxFit.fill,
            )),
      ],
    );
  }

  _getBackgroundColorText(BuildContext context, String title, {String? prefixIconPath}) {
    return Container(
      height: 20.gw,
      padding: EdgeInsets.symmetric(horizontal: 10.gw),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.4),
        borderRadius: BorderRadius.all(Radius.circular(20.gw)),
      ),
      child: Row(
        children: [
          if (prefixIconPath != null) ...[
            Image.asset(prefixIconPath, width: 10.gw, height: 10.gw,),
            SizedBox(width: 3.gw),
          ],
          AneText(
            title,
            style: context.textTheme.secondary.fs12,
          ),
        ],
      ),
    );
  }
}
