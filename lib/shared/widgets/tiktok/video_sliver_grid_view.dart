import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:wd/core/models/entities/video_list_entity.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:wd/shared/widgets/tiktok/home_video_cell_v2.dart';

class VideoSliverGridView extends StatelessWidget {
  final double paddingH;
  final List<VideoListRecords> videoList;
  final ValueChanged<VideoListRecords> onTapCell;
  final int animationKey;

  const VideoSliverGridView({
    super.key,
    this.paddingH = 0,
    required this.videoList,
    required this.onTapCell,
    this.animationKey = 0,
  });

  @override
  Widget build(BuildContext context) {
    final gridSpacing = 11.gw;

    return AnimationLimiter(
      key: ValueKey(animationKey),
      child: SliverGrid(
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2, // 每行两列
          crossAxisSpacing: gridSpacing, // 列之间的间距
          mainAxisSpacing: gridSpacing, // 行之间的间距
          childAspectRatio: 192 / 175, // 宽高比
        ),
        delegate: SliverChildBuilderDelegate(
          (context, index) {
            final model = videoList[index];
            return AnimationConfiguration.staggeredList(
              position: index,
              duration: const Duration(milliseconds: 375),
              child: SlideAnimation(
                verticalOffset: 50.0,
                child: FadeInAnimation(
                  child: InkWell(
                    onTap: () => onTapCell(model),
                    child: HomeVideoCell(model: model),
                  ),
                ),
              ),
            );
          },
          childCount: videoList.length,
        ),
      ),
    );
  }
}
