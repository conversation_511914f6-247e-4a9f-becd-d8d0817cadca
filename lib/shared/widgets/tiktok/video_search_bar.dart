import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/common_textfield.dart';

class VideoSearchBar extends StatefulWidget {
  final String? hintText;
  final GestureTapCallback? onTapSearch;
  final ValueChanged<String>? onChanged;
  final ValueChanged<String>? onSubmitted;
  final bool enable;
  final bool isShowPrefixIcon;
  final TextEditingController controller;
  final Function(bool)? onFocusChanged;
  final bool autofocus;

  const VideoSearchBar({
    super.key,
    this.hintText,
    this.onTapSearch,
    this.onChanged,
    this.onSubmitted,
    required this.controller,
    this.enable = true,
    this.isShowPrefixIcon = true,
    this.onFocusChanged,
    this.autofocus = false,
  });

  @override
  State<StatefulWidget> createState() => _VideoSearchBarState();
}

class _VideoSearchBarState extends State<VideoSearchBar> {

  late FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _focusNode = FocusNode();

    // 添加焦点监听器
    _focusNode.addListener(() {
      if (widget.onFocusChanged != null) {
        widget.onFocusChanged!(_focusNode.hasFocus);
      }
    });
  }

  @override
  void dispose() {
    _focusNode.dispose(); // 记得在dispose中释放focusNode
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return CommonTextField(
      focusNode: _focusNode,
      controller: widget.controller,
      maxHeight: 40.gw,
      autofocus: widget.autofocus,
      onChanged: widget.onChanged,
      onSubmitted: widget.onSubmitted,
      radius: 20.gw,
      hintText: widget.hintText ?? 'please_enter_video_name'.tr(),
      inputEnable: widget.enable,
      hintTextColor: Colors.white.withOpacity(0.6),
      // fillColor: Colors.black.withOpacity(0.2),
      prefixIcon: widget.isShowPrefixIcon
          ? Image.asset("assets/images/tiktok/icon_textField_search.png", width: 14.gw, height: 14.gw)
          : null,
    );
  }

}
