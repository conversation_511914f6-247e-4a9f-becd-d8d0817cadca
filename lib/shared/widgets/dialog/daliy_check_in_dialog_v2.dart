import 'dart:convert';
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:wd/core/constants/base64_image.dart';
import 'package:wd/core/models/entities/daily_check_in_entity.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/features/page/2_activity/activity_list_cubit.dart';
import 'package:wd/features/page/2_activity/activity/check_in_item_widget.dart';
import 'package:wd/shared/widgets/hot_push_image.dart';

class DailyCheckInDialogV2 {
  final void Function(DailyCheckInItem model) onClickCheckIn;

  DailyCheckInDialogV2({
    required this.onClickCheckIn,
  });

  show(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return _DailyCheckInDialogContent(
          onClickCheckIn: onClickCheckIn,
        );
      },
    );
  }
}

class _DailyCheckInDialogContent extends StatefulWidget {
  final void Function(DailyCheckInItem model) onClickCheckIn;

  const _DailyCheckInDialogContent({
    required this.onClickCheckIn,
  });

  @override
  State<StatefulWidget> createState() => _DailyCheckInDialogContentState();
}

class _DailyCheckInDialogContentState extends State<_DailyCheckInDialogContent> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      type: MaterialType.transparency,
      child: Center(
        child: _buildDialogBox(context),
      ),
    );
  }

  Widget _buildDialogBox(BuildContext context) {
    return Container(
      width: 347.gw,
      height: 460.gw,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20.r),
        color: Colors.white,
      ),
      child: BlocBuilder<ActivityListCubit, ActivityListState>(
        builder: (context, state) {
          if (state.checkInModel == null) return Container();

          return Column(
            children: [
              _buildHeader(),
              _buildTotalDays(state.checkInModel!.totalDays.toString()),
              SizedBox(height: 12.gw),
              Expanded(child: _buildContent(context, list: state.checkInModel!.fullList)),
            ],
          );
        },
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      height: 51.gw,
      alignment: Alignment.topCenter,
      decoration: BoxDecoration(
        color: const Color(0xffEFD1B5),
        borderRadius: BorderRadius.circular(12), // 圆角
        boxShadow: const [
          BoxShadow(
            color: Color(0x40693D09), // 阴影颜色 (0x40 = 25% 透明度)
            offset: Offset(0, 4.54), // X: 0, Y: 4.54px
            blurRadius: 4.54, // 模糊半径 4.54px
          ),
        ],
      ),
      child: Container(
        height: 48.gw,
        decoration: BoxDecoration(
          color: const Color(0xFFF3EAE1), // 背景色
          borderRadius: BorderRadius.circular(12), // 圆角
          boxShadow: const [
            BoxShadow(
              color: Color(0x1A000000), // 阴影颜色，10% 透明度
              offset: Offset(0, 5), // X: 0, Y: 5px
              blurRadius: 5, // 模糊 5px
            ),
          ],
        ),
        padding: EdgeInsets.only(left: 18.gw),
        child: Row(
          children: [
            HotPushImage(
              imagePath: "assets/images/check_in/icon_check_in_dialog_title.png",
              base64String: Base64Image.checkInDialogTitleBgData,
              width: 108.gw,
              height: 24.gw,
            ),
            const Spacer(),
            InkWell(
                onTap: Navigator.of(context).pop,
                child: Container(
                  width: 60.gw,
                  height: 40.gw,
                  alignment: Alignment.center,
                  child: HotPushImage(
                    imagePath: "assets/images/check_in/btn_check_in_dialog_close.png",
                    base64String: Base64Image.checkInDialogCloseBtnData,
                    width: 14.gw,
                    height: 14.gw,
                  ),
                ))
          ],
        ),
      ),
    );
  }

  Widget _buildTotalDays(String total) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 12.gw),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.baseline,
        textBaseline: TextBaseline.alphabetic,
        children: [
          Text(
            "当前已签到",
            style: TextStyle(color: const Color(0xff323232), fontSize: 12.fs),
          ),
          SizedBox(width: 8.gw),
          Text(
            total,
            style: TextStyle(color: const Color(0xffC0662E), fontSize: 32.fs, fontFamily: "Impact"),
          ),
          SizedBox(width: 5.gw),
          Text(
            "天",
            style: TextStyle(color: const Color(0xffC0662E), fontSize: 10.fs),
          ),
          const Spacer(),
        ],
      ),
    );
  }

  double get signCardItemWidth => ((GSScreenUtil().screenWidth - 14.gw * 2 - 8.gw * 2) - (6 * 5.gw)) / 7;

  Widget _buildContent(BuildContext context, {required List<DailyCheckInItem> list}) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 14.gw),
      width: double.infinity, // 限制宽度
      child: GridView.builder(
        padding: EdgeInsets.only(bottom: 10.gw),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 7, mainAxisSpacing: 5.gw, crossAxisSpacing: 5.gw, childAspectRatio: 43 / 67),
        itemBuilder: (context, index) {
          final e = list[index];
          return CheckInItemWidget(
            model: e,
            width: signCardItemWidth,
            onClickCheckIn: widget.onClickCheckIn,
          );
        },
        itemCount: list.length,
      ),
    );
  }

}
