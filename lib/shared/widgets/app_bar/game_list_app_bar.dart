import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/screenUtil.dart';

class GameListAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final GestureTapCallback onTapTitle;
  final List<Widget>? actions;
  final bool isExpanded;

  const GameListAppBar({
    super.key,
    required this.title,
    required this.onTapTitle,
    required this.isExpanded,
    this.actions,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      flexibleSpace: Container(
        color: isExpanded ? context.colorTheme.highlightForeground : context.theme.appBarTheme.backgroundColor,
      ),
      leading: IconButton(
        onPressed: () => Navigator.of(context).pop(),
        icon: const Image(
          image: AssetImage("assets/images/toolBar/icon_toolBar_back.png"),
          height: 20,
          width: 20,
        ),
      ),
      actions: [
        InkWell(
          onTap: onTapTitle,
          child: Container(
            height: 33.gw,
            padding: EdgeInsets.fromLTRB(16.gw, 0, 14.5.gw, 0),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8.gw),
                color: isExpanded ? context.colorTheme.tabItemBgA : context.theme.cardColor,
                border:
                    Border.all(color: isExpanded ? context.colorTheme.borderE : context.theme.dividerColor, width: 1)),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(title, style: context.textTheme.primary),
                SizedBox(width: 8.gw),
                AnimatedRotation(
                  duration: const Duration(milliseconds: 300),
                  turns: isExpanded ? 0.5 : 0, // 四分之一圈（顺时针/逆时针）
                  child: SvgPicture.asset(
                    "assets/images/toolBar/icon_toolBar_arrow_down.svg",
                    width: 8.gw,
                    height: 4.gw,
                    colorFilter: ColorFilter.mode(
                      isExpanded
                          ? context.theme.bottomNavigationBarTheme.selectedItemColor!
                          : context.theme.bottomNavigationBarTheme.unselectedItemColor!,
                      BlendMode.srcIn,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        SizedBox(width: 20.gw),
      ],
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
