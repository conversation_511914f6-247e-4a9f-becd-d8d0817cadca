import 'package:flutter/material.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';

class HeaderContentCard extends StatelessWidget {
  const HeaderContentCard({
    super.key,
    required this.header,
    required this.content,
    this.radius = 6,
    this.margin,
  });

  final Widget header;
  final Widget content;
  final double radius;
  final EdgeInsets? margin;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin ?? EdgeInsets.symmetric(horizontal: 6.gw),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Container(
            padding: EdgeInsets.all(16.gw),
            decoration: BoxDecoration(
              color: context.theme.dividerColor,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(radius.gw),
                topRight: Radius.circular(radius.gw),
              ),
            ),
            child: header,
          ),
          Container(
            decoration: BoxDecoration(
              color: context.theme.cardColor,
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(6.gw),
                bottomRight: Radius.circular(6.gw),
              ),
            ),
            child: content,
          ),
        ],
      ),
    );
  }
}
