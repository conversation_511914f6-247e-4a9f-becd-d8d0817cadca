import 'package:flutter/material.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';

class IconTextfield extends StatelessWidget {
  const IconTextfield(
      {super.key, required this.textController, required this.hintText, required this.icon, this.onIconPressed});

  final TextEditingController textController;
  final String hintText;
  final IconData icon;
  final VoidCallback? onIconPressed;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.fromLTRB(4.gw, 0, 0, 0),
      decoration: BoxDecoration(
        color: context.colorTheme.foregroundColor,
        borderRadius: BorderRadius.circular(12.gw),
      ),
      child: Row(
        children: [
          IconButton(icon: Icon(icon), color: context.colorTheme.textTitle, onPressed: onIconPressed),
          Expanded(
            child: TextField(
              controller: textController,
              decoration: InputDecoration(
                hintText: hintText,
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.only(
                    topRight: Radius.circular(12.gw),
                    bottomRight: Radius.circular(12.gw),
                  ),
                ),
              ),
              style: const TextStyle(
                color: Color(0xFF333333),
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
