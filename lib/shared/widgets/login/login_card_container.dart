import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:keyboard_dismisser/keyboard_dismisser.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';

class LoginCardContainer extends StatefulWidget {
  final Widget child;
  const LoginCardContainer({super.key, required this.child});

  @override
  State<LoginCardContainer> createState() => _LoginCardContainerState();
}

class _LoginCardContainerState extends State<LoginCardContainer> {
  final ScrollController _scrollController = ScrollController();
  late KeyboardVisibilityController _keyboardVisibilityController;

  @override
  void initState() {
    super.initState();
    _keyboardVisibilityController = KeyboardVisibilityController();
    
    // 添加键盘监听
    _keyboardVisibilityController.onChange.listen((bool visible) {
      if (visible) {
        if (!kIsWeb) {
          Future.delayed(const Duration(milliseconds: 100), () {
            _handleScroll();
          });
        }
      } else {
        // 键盘隐藏时滚动回顶部
        _scrollToTop();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;

    return KeyboardDismisser(
      child: Scaffold(
        backgroundColor: Colors.white,
        extendBodyBehindAppBar: false,
        resizeToAvoidBottomInset: true,
        body: Focus(
          onFocusChange: (hasFocus) {
            if (kIsWeb) {
              if (hasFocus) {
                _handleWebScroll();
              } else {
                // Web端失去焦点时滚动回顶部
                _scrollToTop();
              }
            }
          },
          child: SingleChildScrollView(
            controller: _scrollController,
            physics: const ClampingScrollPhysics(),
            child: ConstrainedBox(
              constraints: BoxConstraints(
                minHeight: screenHeight + 100,
              ),
              child: Column(
                children: [
                  Stack(
                    children: [
                      SizedBox(
                        height: 250.gw,
                        width: double.infinity,
                        child: Image.asset(
                          'assets/images/login/bg_login_logo.png',
                          fit: BoxFit.cover,
                        ),
                      ),
                      _buildBackBtn(context),
                    ],
                  ),
                  _buildCardView(context),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _handleScroll() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        250.gw,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  void _handleWebScroll() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        150.gw,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  void _scrollToTop() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        0,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  _buildBackBtn(context) {
    return Padding(
      padding: EdgeInsets.only(top: ScreenUtil().statusBarHeight, left: 15),
      child: GestureDetector(
        onTap: () => Navigator.of(context).pop(),
        child: Container(
          width: 44,
          height: 44,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(22.0),
            color: Colors.black.withOpacity(0.1),
          ),
          alignment: Alignment.center,
          child: const Image(
            image: AssetImage("assets/images/toolBar/icon_toolBar_back.png"),
            height: 20,
            width: 20,
          ),
        ),
      ),
    );
  }

  _buildCardView(context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            blurRadius: 50,
            offset: const Offset(0, -15.8),
            color: const Color(0xff552E00).withOpacity(0.28),
          )
        ],
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(15.gw),
          topRight: Radius.circular(15.gw),
        ),
      ),
      child: Padding(
        padding: EdgeInsets.all(20.0),
        child: widget.child,
      ),
    );
  }
}
