import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:wd/core/base/base_stateful_page.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/auth_util.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/features/page/main/screens/main_screen_cubit.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/common_button.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';

class ChatEntranceBanner extends StatelessWidget {
  final String? imagePath;
  final EdgeInsets? margin;
  final double? height;

  const ChatEntranceBanner({super.key, this.imagePath, this.margin, this.height});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        AuthUtil.checkIfLogin(() async {
          sl<MainScreenCubit>().goToChatPage();
        });
      },
      child: AspectRatio(
        aspectRatio: 400/154,
        child: Container(
          margin: margin,
          padding: EdgeInsets.only(left: 16.gw, top: 25.gw),
          width: double.infinity,
          clipBehavior: Clip.hardEdge,
          decoration: BoxDecoration(
              image: DecorationImage(
            image: AssetImage(imagePath ?? "assets/images/chat/banner/bg_adv_a.png"),
            fit: BoxFit.fill,
          )),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text("official".tr(), style: context.textTheme.primary.fs32.w700.ffAne),
              Text("chatroom".tr(), style: context.textTheme.secondary.fs32.w700.ffAne),
              // SizedBox(height: 12.gw),
              CommonButton(
                title: 'tap_to_enter'.tr(),
                width: 93.gw,
                height: 29.gw,
                fontSize: 14.gw,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
