import 'package:flutter/material.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';

class TransactSectionWidget extends StatelessWidget {
  final Widget child;

  const TransactSectionWidget({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    return Container(
        margin: EdgeInsets.fromLTRB(20.gw, 20.gw, 20.gw, 0),
        clipBehavior: Clip.hardEdge,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.gw),
          color: context.theme.cardColor,
        ),
        child: child);
  }
}
