import 'package:flutter/material.dart';
import 'package:wd/core/base/base_stateful_page.dart';
import 'package:wd/core/theme/text/app_text_theme.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:text_scroll/text_scroll.dart';

import 'transact_tag_widget.dart';

class TransactSelButton extends StatelessWidget {
  final String title;
  final String? content;
  final bool isSelected;
  final bool isRecommended;
  final bool isAmount; // 是否为金额显示
  final Widget? prefix;
  final double fontSize;
  final Color selectedBorderColor;
  final String? payChannelTag;

  TransactSelButton({
    super.key,
    required this.title,
    required this.isSelected,
    this.isRecommended = false,
    this.prefix,
    this.content,
    this.isAmount = false,
    double? fontSize,
    Color? selectedBorderColor,
    this.payChannelTag,
  })  : fontSize = fontSize ?? 16.fs,
        selectedBorderColor = selectedBorderColor ?? const Color(0xffCDB296);

  @override
  Widget build(BuildContext context) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        Positioned.fill(
          child: Container(
            margin: EdgeInsets.only(bottom: 2.gw),
            clipBehavior: Clip.hardEdge,
            decoration: BoxDecoration(
              color: isSelected ? context.colorTheme.foregroundColor : context.theme.cardColor,
              border: Border.all(
                  color: isSelected ? context.theme.primaryColor : context.colorTheme.foregroundColor, width: 1),
              borderRadius: BorderRadius.circular(6.gw),
            ),
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 10.gw, vertical: 5.gw),
              child: content == null
                  ? Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        if (prefix != null) Padding(padding: EdgeInsets.only(right: 4.gw), child: prefix),
                        Flexible(
                          child: _buildTitleWidget(context),
                        ),
                      ],
                    )
                  : Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildTitleWidget(context),
                        SizedBox(height: 1.5.gw),
                        TextScroll(
                          content!,
                          style:
                              TextStyle(fontSize: 12.fs, color: const Color(0xffB39572), fontWeight: FontWeight.w500),
                          mode: TextScrollMode.endless,
                          velocity: const Velocity(pixelsPerSecond: Offset(20, 0)),
                          delayBefore: const Duration(milliseconds: 500),
                        ),
                      ],
                    ),
            ),
          ),
        ),
        if (payChannelTag != null && payChannelTag!.isNotEmpty)
          Positioned(top: -8.gw, right: -6, child: TransactTagWidget(title: payChannelTag!))
      ],
    );
  }

  Widget _buildTitleWidget(BuildContext context ){
    return TextScroll(
  title,
      mode: TextScrollMode.endless,
      velocity: const Velocity(pixelsPerSecond: Offset(20, 0)),
      delayBefore: const Duration(milliseconds: 500),
      style: context.textTheme.secondary.fs16.w500,
    );
  }
}
