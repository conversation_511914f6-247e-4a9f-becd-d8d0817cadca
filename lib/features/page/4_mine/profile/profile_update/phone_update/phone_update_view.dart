import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/theme/themes.dart';


import '../../../../../../core/base/base_state.dart';
import '../../../../../../core/constants/constants.dart';

import '../../../../../../injection_container.dart';
import '../../../../../../shared/widgets/common_button.dart';
import '../../../../../../shared/widgets/common_textfield.dart';
import '../../../../../../shared/widgets/easy_loading.dart';
import '../../../../../../shared/widgets/verification_code/verification_code.dart';
import '../../../../../routers/navigator_utils.dart';
import 'phone_update_cubit.dart';

class PhoneUpdateWidget extends StatefulWidget {
  final String phoneNo;
  const PhoneUpdateWidget({super.key, required this.phoneNo});

  @override
  State<PhoneUpdateWidget> createState() => _PhoneUpdateWidgetState();
}

class _PhoneUpdateWidgetState extends State<PhoneUpdateWidget> {
  final phoneController = TextEditingController();
  final verificationCodeController = TextEditingController();
  final newVerificationCodeController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<PhoneUpdateCubit, PhoneUpdateState>(
      builder: (context, state) {
        return SingleChildScrollView(
          child: Column(
            children: [
              if (widget.phoneNo.isNotEmpty) ...[
                _buildCurrentPhoneSection(context),
                SizedBox(height: 16.gw),
                _buildVerificationCodeSection(context),
                SizedBox(height: 16.gw),
                _buildNewPhoneSection(context),
              ] else ...[
                _buildNewPhoneSection(context),
              ],
              SizedBox(height: 32.gw),
              _buildSaveButton(context, widget.phoneNo.isEmpty),
            ],
          ),
        );
      },
    );
  }

  Widget _buildCurrentPhoneSection(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(24.gw),
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(12.gw),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'current_phone_number'.tr(),
            style: context.textTheme.title.fs20.w500.copyWith(
              color: context.colorTheme.textSecondary,
            ),
          ),
          Text(
            widget.phoneNo.replaceRange(3, 7, '****'),
            style: context.textTheme.title.fs20.w500.copyWith(
              color: context.colorTheme.btnBgPrimary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVerificationCodeSection(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.gw),
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(12.gw),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'hint_enter_verification_code'.tr(),
            style: context.textTheme.title.fs20.w500,
          ),
          SizedBox(height: 32.gw),
          _buildVerificationCodeInput(context, phoneNo: widget.phoneNo),
        ],
      ),
    );
  }

  Widget _buildNewPhoneSection(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.gw),
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(12.gw),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.phoneNo.isNotEmpty ? 'phone_no_change_tips'.tr() : 'phone_no_tips'.tr(),
            style: context.textTheme.title.fs20.w500,
          ),
          SizedBox(height: 32.gw),
          _buildPhoneInput(context),
          SizedBox(height: 16.gw),
          _buildVerificationCodeInput(
            context,
            phoneNo: widget.phoneNo,
            isNew: true,
          ),
        ],
      ),
    );
  }

  Widget _buildSaveButton(BuildContext context, bool isUpdate) {
    return BlocListener<PhoneUpdateCubit, PhoneUpdateState>(
      listenWhen: (previous, current) => previous.updateStatus != current.updateStatus,
      listener: (context, state) async {
        if (state.updateStatus == SimplyNetStatus.success) {
          await sl<UserCubit>().fetchUserInfo();
          sl<NavigatorService>().pop();
        }
      },
      child: CommonButton(
        enable: true,
        title: "update".tr(),
        style: CommonButtonStyle.primary,
        onPressed: () => _handleUpdatePress(context),
      ),
    );
  }

  Widget _buildPhoneInput(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(
          color: context.colorTheme.borderA,
          width: 1,
        ),
        borderRadius: BorderRadius.circular(12.gw),
      ),
      child: Row(
        children: [
          _buildPhonePrefix(context),
          Expanded(
            child: TextField(
              controller: phoneController,
              style: context.textTheme.regular.fs16,
              decoration: InputDecoration(
                border: InputBorder.none,
                hintText: "enter_phone".tr(),
                hintStyle: context.textTheme.secondary.fs13,
                contentPadding: EdgeInsets.symmetric(
                  horizontal: 20.gw,
                  vertical: 18.gw,
                ),
              ),
              keyboardType: TextInputType.phone,
              onChanged: (value) => context.read<PhoneUpdateCubit>().setPhoneNo(value),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPhonePrefix(BuildContext context) {
    return Container(
      width: 56.gw,
      height: 60.gw,
      decoration: BoxDecoration(
        color: context.colorTheme.borderA,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(12.gw),
          bottomLeft: Radius.circular(12.gw),
        ),
      ),
      child: Center(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              '+971',
              style: context.textTheme.title.fs13.w500.copyWith(
                color: context.colorTheme.textSecondary,
              ),
            ),
            SizedBox(width: 4.gw),
            Icon(
              Icons.keyboard_arrow_down,
              color: context.colorTheme.textSecondary,
              size: 12.gw,
            ),
          ],
        ),
      ),
    );
  }


  Widget _buildVerificationCodeInput(
    BuildContext context, {
    bool isNew = false,
    required String phoneNo,
  }) {
    return CommonTextField(
      controller: isNew ? newVerificationCodeController : verificationCodeController,
      hintText: "hint_enter_verification_code".tr(),
      keyboardType: TextInputType.number,
      prefixIcon: _buildVerificationPrefix(),
      suffixIcon: BlocBuilder<PhoneUpdateCubit, PhoneUpdateState>(
        builder: (context, state) => VerificationCode(
          phone: isNew ? state.phoneNo : phoneNo,
          checkIsBind: isNew,
          isGradient: true,
          onSmsCode: (smsCode ) {
            if (kDebug && smsCode.isNotEmpty) {
              if (isNew) {
                newVerificationCodeController.text = smsCode;
              } else {
                verificationCodeController.text = smsCode;
              }
            }
          },
        ),
      ),
      suffixIconPadding: EdgeInsets.zero,
      prefixIconPadding: EdgeInsets.zero,
      onChanged: (_) {},
    );
  }

  Widget _buildVerificationPrefix() {
    return Container(
      padding: EdgeInsets.only(left: 10.gw, right: 14.gw),
      child: Image.asset(
        "assets/images/login/shield.png",
        color: const Color(0xffEACA9F),
        width: 16.gw,
        height: 16.gw,
      ),
    );
  }



  void _handleUpdatePress(BuildContext context) {
    final String phoneNo = phoneController.text.isNotEmpty ? phoneController.text : widget.phoneNo;
    final String verificationCode = verificationCodeController.text;
    final String newVerificationCode = newVerificationCodeController.text;

    if (phoneController.text.isEmpty) {
      GSEasyLoading.showToast('请输入新手机号');
      return;
    }

    if (verificationCode.isEmpty && widget.phoneNo.isNotEmpty) {
      GSEasyLoading.showToast('输入验证码');
      return;
    }

    if (widget.phoneNo.isNotEmpty && newVerificationCode.isEmpty) {
      GSEasyLoading.showToast('输入验证码');
      return;
    }

    if (widget.phoneNo.isEmpty) {
      context.read<PhoneUpdateCubit>().bindPhoneNo(
            phoneNo: phoneNo,
            code: newVerificationCode,
          );
    } else {
      context.read<PhoneUpdateCubit>().updatePhoneNo(
            oldPhoneSmsCode: verificationCode,
            newPhoneNo: phoneNo,
            newPhoneSmsCode: newVerificationCode,
          );
    }
  }
}
