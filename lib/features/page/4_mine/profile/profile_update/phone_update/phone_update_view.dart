import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/auth/phone_prefix.dart';

import '../../../../../../core/base/base_state.dart';
import '../../../../../../core/constants/constants.dart';
import '../../../../../../core/singletons/user_singleton.dart';
import '../../../../../../injection_container.dart';
import '../../../../../../shared/widgets/common_button.dart';
import '../../../../../../shared/widgets/common_textfield.dart';
import '../../../../../../shared/widgets/easy_loading.dart';
import '../../../../../../shared/widgets/verification_code/verification_code.dart';
import '../../../../../routers/navigator_utils.dart';
import 'phone_update_cubit.dart';

class PhoneUpdateWidget extends StatefulWidget {
  final String phoneNo;
  const PhoneUpdateWidget({super.key, required this.phoneNo});

  @override
  State<PhoneUpdateWidget> createState() => _PhoneUpdateWidgetState();
}

class _PhoneUpdateWidgetState extends State<PhoneUpdateWidget> {
  final phoneController = TextEditingController();
  final verificationCodeController = TextEditingController();
  final newVerificationCodeController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<PhoneUpdateCubit, PhoneUpdateState>(
      builder: (context, state) {
        return SingleChildScrollView(
          child: Column(
            children: [
              widget.phoneNo.isNotEmpty ? _buildExistingPhoneInput(context) : _buildNewPhoneInput(context),
              SizedBox(height: 20.gw),
              _buildUpdateButton(context, widget.phoneNo.isEmpty),
            ],
          ),
        );
      },
    );
  }

  Widget _buildNewPhoneInput(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(14.gw),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: Column(
        children: [
          _buildPhoneInput(context),
          SizedBox(height: 12.gw),
          _buildVerificationCodeInput(
            context,
            phoneNo: widget.phoneNo,
            isNew: true,
          ),
        ],
      ),
    );
  }

  Widget _buildExistingPhoneInput(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildTitle(context, "当前手机号：${widget.phoneNo.replaceRange(3, 9, '****')}"),
        Container(
          height: 80.gw,
          padding: EdgeInsets.all(14.gw),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8.0),
          ),
          child: _buildVerificationCodeInput(context, phoneNo: widget.phoneNo),
        ),
        _buildTitle(context, "更换绑定手机号"),
        Container(
          // height: 140.gw,
          padding: EdgeInsets.all(14.gw),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8.0),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildPhoneInput(context),
              SizedBox(height: 12.gw),
              _buildVerificationCodeInput(
                context,
                isNew: true,
                phoneNo: widget.phoneNo,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTitle(BuildContext context, String title) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8.gw),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium!.copyWith(fontSize: 12.gw),
      ),
    );
  }

  Widget _buildPhoneInput(BuildContext context) {
    return CommonTextField(
      controller: phoneController,
      hintText: "hint_enter_phone".tr(),
      keyboardType: TextInputType.phone,
      maxLength: 11,
      suffixIconPadding: EdgeInsets.zero,
      prefixIconPadding: EdgeInsets.zero,
      prefixIcon: const PhonePrefix(showIcon: true),
      onChanged: (value) => context.read<PhoneUpdateCubit>().setPhoneNo(value),
    );
  }


  Widget _buildVerificationCodeInput(
    BuildContext context, {
    bool isNew = false,
    required String phoneNo,
  }) {
    return CommonTextField(
      controller: isNew ? newVerificationCodeController : verificationCodeController,
      hintText: "hint_enter_verification_code".tr(),
      keyboardType: TextInputType.number,
      prefixIcon: _buildVerificationPrefix(),
      suffixIcon: BlocBuilder<PhoneUpdateCubit, PhoneUpdateState>(
        builder: (context, state) => VerificationCode(
          phone: isNew ? state.phoneNo : phoneNo,
          checkIsBind: isNew,
          isGradient: true,
          onSmsCode: (smsCode ) {
            if (kDebug && smsCode.isNotEmpty) {
              if (isNew) {
                newVerificationCodeController.text = smsCode;
              } else {
                verificationCodeController.text = smsCode;
              }
            }
          },
        ),
      ),
      suffixIconPadding: EdgeInsets.zero,
      prefixIconPadding: EdgeInsets.zero,
      onChanged: (_) {},
    );
  }

  Widget _buildVerificationPrefix() {
    return Container(
      padding: EdgeInsets.only(left: 10.gw, right: 14.gw),
      child: Image.asset(
        "assets/images/login/shield.png",
        color: const Color(0xffEACA9F),
        width: 16.gw,
        height: 16.gw,
      ),
    );
  }

  Widget _buildUpdateButton(BuildContext context, bool isUpdate) {
    return BlocListener<PhoneUpdateCubit, PhoneUpdateState>(
      listenWhen: (previous, current) => previous.updateStatus != current.updateStatus,
      listener: (context, state) async {
        if (state.updateStatus == SimplyNetStatus.success) {
          await sl<UserCubit>().fetchUserInfo();
          sl<NavigatorService>().pop();
        }
      },
      child: CommonButton(
        title: isUpdate ? "绑定手机号" : "更新",
        onPressed: () => _handleUpdatePress(context),
      ),
    );
  }

  void _handleUpdatePress(BuildContext context) {
    final String phoneNo = phoneController.text.isNotEmpty ? phoneController.text : widget.phoneNo;
    final String verificationCode = verificationCodeController.text;
    final String newVerificationCode = newVerificationCodeController.text;

    if (phoneController.text.isEmpty) {
      GSEasyLoading.showToast('请输入新手机号');
      return;
    }

    if (verificationCode.isEmpty && widget.phoneNo.isNotEmpty) {
      GSEasyLoading.showToast('输入验证码');
      return;
    }

    if (widget.phoneNo.isNotEmpty && newVerificationCode.isEmpty) {
      GSEasyLoading.showToast('输入验证码');
      return;
    }

    if (widget.phoneNo.isEmpty) {
      context.read<PhoneUpdateCubit>().bindPhoneNo(
            phoneNo: phoneNo,
            code: newVerificationCode,
          );
    } else {
      context.read<PhoneUpdateCubit>().updatePhoneNo(
            oldPhoneSmsCode: verificationCode,
            newPhoneNo: phoneNo,
            newPhoneSmsCode: newVerificationCode,
          );
    }
  }
}
