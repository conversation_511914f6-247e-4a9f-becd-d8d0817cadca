import 'package:bloc/bloc.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/widgets.dart';
import 'package:wd/core/constants/enums.dart';
import 'package:wd/core/models/apis/user.dart';
import 'package:wd/core/singletons/user_cubit.dart';

import 'package:wd/core/utils/log_util.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/easy_loading.dart';
import 'update_profile_state.dart';

class UpdateProfileCubit extends Cubit<UpdateProfileState> {
  UpdateProfileCubit(String initialValue, this.field)
      : super(UpdateProfileState(
          value: initialValue,
          controller: TextEditingController(text: initialValue),
        ));

  final UserFieldType field;

  void valueChanged(String value) {
    LogI('Value changed: $value');
    emit(state.copyWith(value: value));
  }

  bool validateInput() {
    if (state.value.isEmpty) {
      return false;
    }
    if (field == UserFieldType.realName) {
      if (StringUtil.containsSpecialCharsOrSpaces(state.value)) {
        const errMsg = '「姓名」请勿包含空格或特殊字符';
        emit(state.copyWith(errorMessage: errMsg));
        GSEasyLoading.showToast(errMsg.tr());
        return false;
      }
      if (StringUtil.containsNumbers(state.value)) {
        const errMsg = '「姓名」请勿包含数字';
        emit(state.copyWith(errorMessage: errMsg));
        GSEasyLoading.showToast(errMsg.tr());
        return false;
      }
    }
    if (field == UserFieldType.phone && StringUtil.containsSpecialCharsOrSpaces(state.value)) {
      const errMsg = '「手机号」请勿包含空格或特殊字符';
      emit(state.copyWith(errorMessage: errMsg));
      GSEasyLoading.showToast(errMsg.tr());
      return false;
    }

    if (field == UserFieldType.phone && state.value.length < 11) {
      const errMsg = 'enter_phone_limit';
      emit(state.copyWith(errorMessage: errMsg));
      GSEasyLoading.showToast(errMsg.tr());
      return false;
    }
    if (field == UserFieldType.email && !RegExp(r'^[^@]+@[^@]+\.[^@]+').hasMatch(state.value)) {
      const errMsg = 'enter_email_limit';
      emit(state.copyWith(errorMessage: errMsg));
      GSEasyLoading.showToast(errMsg.tr());
      return false;
    }
    return true;
  }

  Future<void> updateField({String? password}) async {
    emit(state.copyWith(errorMessage: null));
    if ((field == UserFieldType.phone || field == UserFieldType.email) && password == null) {
      LogE('Password is required for updating phone or email');
      emit(state.copyWith(updateSuccess: false));
      return;
    }

    if (!validateInput()) {
      emit(state.copyWith(updateSuccess: false));
      return;
    }

    await performApiUpdate(
      apiCall: () async {
        switch (field) {
          case UserFieldType.realName:
            return await UserApi.setRealName(state.value);
          case UserFieldType.nickName:
            return await UserApi.updateNickName(state.value);
          case UserFieldType.phone:
            return await UserApi.updatePhone(state.value, password!);
          case UserFieldType.email:
            return await UserApi.updateEmail(state.value, password!);
          default:
            LogE('Unknown field: $field');
            return (false);
        }
      },
      loadingMessage: '更新中...',
      onSuccess: () {
        sl<UserCubit>().fetchUserInfo();
        GSEasyLoading.showToast('update_successful'.tr());
        emit(state.copyWith(updateSuccess: true));
        LogI('$field updated successfully');
        if (password != null) sl<UserCubit>().resetFundPasswordErrorLock();
      },
      onFailure: () {
        LogE('Error updating $field');
        emit(state.copyWith(updateSuccess: false));
      },
    );
  }

  Future<void> performApiUpdate({
    required Future<bool> Function() apiCall,
    required String loadingMessage,
    required VoidCallback onSuccess,
    required VoidCallback onFailure,
  }) async {
    try {
      GSEasyLoading.showLoading(message: loadingMessage);
      final success = await apiCall();
      if (success) {
        onSuccess();
      } else {
        onFailure();
      }
    } catch (e) {
      LogE('Exception occurred: $e');
      onFailure();
    } finally {
      GSEasyLoading.dismiss();
    }
  }

  void dispose() {
    state.controller.dispose();
  }
}
