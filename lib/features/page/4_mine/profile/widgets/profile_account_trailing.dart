import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/clipboardTool.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';

import '../../../../../core/constants/assets.dart';

/// Account trailing widget with copy functionality and forward arrow
class ProfileAccountTrailing extends StatelessWidget {
  final String account;

  const ProfileAccountTrailing({
    super.key,
    required this.account,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        GestureDetector(
          onTap: () => ClipboardTool.setDataToast(account,
              msg: 'copied_to_clipboard'.tr()),
          child: Container(
            height: 32.gw,
            decoration: BoxDecoration(
              color: context.theme.cardColor,
              borderRadius: BorderRadius.circular(6.gw),
              border: Border.all(color: context.colorTheme.borderA),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 12.gw),
                  child: AneText(
                    account,
                    style: context.textTheme.title.fs16,
                  ),
                ),
                Container(
                  width: 32.gw,
                  height: 32.gw,
                  decoration: BoxDecoration(
                    color: context.colorTheme.borderA,
                    borderRadius: BorderRadius.only(
                      topRight: Radius.circular(6.gw),
                      bottomRight: Radius.circular(6.gw),
                    ),
                  ),
                  child: Center(
                    child: SvgPicture.asset(
                      Assets.iconCopy,
                      width: 12.gw,
                      height: 12.gw,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        SizedBox(width: 17.gw),
        SvgPicture.asset(
          Assets.iconForward_v2,
          width: 6.gw,
          height: 12.gw,
        ),
      ],
    );
  }
}
