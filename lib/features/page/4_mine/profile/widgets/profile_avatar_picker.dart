import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/common_button.dart';
import 'package:wd/shared/widgets/common_bottom_sheet.dart';

import '../profile_cubit.dart';
import '../profile_state.dart';

/// Utility class for showing avatar picker modal
class ProfileAvatarPicker {
  static Future<void> show(BuildContext context) async {
    final userInfoCubit = context.read<UserProfileCubit>();
    final originalAvatarId = userInfoCubit.state.avatarId;
    userInfoCubit.loadAvatars();

    await CommonBottomSheet.show(
      context: context,
      maxHeight: 431.gw,
      backgroundColor: context.theme.cardColor,
      showGradientOverlay: true,
      gradientOverlayHeight: 100.0,
      isScrollControlled: false,
      contentPadding: EdgeInsets.symmetric(horizontal: 18.gw, vertical: 16.gw),
      header: BottomSheetHeader(
        title: 'choose_avatar'.tr(),
        showCloseButton: false,
      ),
      buttons: [
        BottomSheetButton(
          title: 'cancel'.tr(),
          style: CommonButtonStyle.secondary,
          backgroundColor: context.colorTheme.foregroundColor,
          borderColor: context.colorTheme.borderA,
          onPressed: () {
            userInfoCubit.resetTempValues(originalAvatarId: originalAvatarId);
            Navigator.pop(context);
          },
        ),
        BottomSheetButton(
          title: 'ok'.tr(),
          style: CommonButtonStyle.primary,
          backgroundColor: context.colorTheme.btnBgPrimary,
          textColor: context.colorTheme.btnTitlePrimary,
          borderColor: context.colorTheme.btnBorderPrimary,
          onPressed: () {
            userInfoCubit.confirmAvatar();
            Navigator.pop(context);
          },
        ),
      ],
      children: [
        BlocProvider.value(
          value: userInfoCubit,
          child: const AvatarGridContent(),
        ),
      ],
    );

    userInfoCubit.resetTempValues(originalAvatarId: originalAvatarId);
  }
}

/// Avatar grid content widget for the custom bottom sheet
class AvatarGridContent extends StatelessWidget {
  const AvatarGridContent({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<UserProfileCubit, UserProfileState>(
      builder: (context, state) {
        return _buildAvatarGrid(context, state);
      },
    );
  }



  Widget _buildAvatarGrid(BuildContext context, UserProfileState state) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 5, // Changed to 5 columns to match design
        childAspectRatio: 1,
        crossAxisSpacing: 16.gw,
        mainAxisSpacing: 16.gw,
      ),
      itemCount: state.avatars.length,
      itemBuilder: (context, index) =>
          _buildAvatarItem(context, state, index),
    );
  }



  Widget _buildAvatarItem(
      BuildContext context, UserProfileState state, int index) {
    final isSelected = state.tempAvatarId == index + 1;
    return GestureDetector(
      onTap: () => context.read<UserProfileCubit>().setTempAvatar(index + 1),
      child: Container(
        width: 60.gw,
        height: 60.gw,
        decoration: BoxDecoration(
          border: Border.all(
            color: isSelected
                ? context.colorTheme.btnBgPrimary
                : context.colorTheme.borderA,
            width: 1.19,
          ),
          borderRadius: BorderRadius.circular(12.gw),
        ),
        child: Padding(
          padding: EdgeInsets.all(2.38.gw),
          child: Stack(
            clipBehavior: Clip.none,
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(9.53.gw),
                child: Image.asset(
                  state.avatars[index],
                  width: double.infinity,
                  height: double.infinity,
                  fit: BoxFit.cover,
                ),
              ),
              if (isSelected)
                Positioned(
                  top: -8.gw,
                  right: -8.gw,
                  child: Container(
                    width: 15.gw,
                    height: 15.gw,
                    decoration: BoxDecoration(
                      color: context.colorTheme.btnBgPrimary,
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.check,
                      size: 10.gw,
                      color: context.colorTheme.btnTitlePrimary,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
