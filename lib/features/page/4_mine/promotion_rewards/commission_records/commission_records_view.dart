// commission_records_view.dart
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/message.dart';
import '../../../../../core/base/common_refresher.dart';
import '../../../../../core/models/apis/promotion.dart';
import '../../../../../core/models/entities/commission_details_entity.dart';
import '../../../../../injection_container.dart';
import '../../../../../shared/widgets/promotion/search_field.dart';
import '../../../../../shared/widgets/promotion/tab_bar_promotion.dart';
import '../../../../../shared/widgets/promotion/tab_bar_promotion_records.dart';
import '../../../../routers/navigator_utils.dart';
import 'commission_records_cubit.dart';

// View
class CommissionRecordsView extends StatelessWidget {
  const CommissionRecordsView({super.key});

  @override
  Widget build(BuildContext context) => BlocProvider(
        create: (_) => CommissionRecordsCubit()..fetchCommissionRecords(),
        child: const _CommissionRecordsContent(),
      );
}

class _CommissionRecordsContent extends StatefulWidget {
  const _CommissionRecordsContent();

  @override
  State<_CommissionRecordsContent> createState() => _CommissionRecordsContentState();
}

class _CommissionRecordsContentState extends State<_CommissionRecordsContent> with SingleTickerProviderStateMixin {
  late final TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: DayType.values.length, vsync: this)..addListener(_onTabChanged);
  }

  void _onTabChanged() {
    context.read<CommissionRecordsCubit>().resetCommissionDetailsEntity();
    if (_tabController.indexIsChanging) return;
    if (_tabController.index != _tabController.previousIndex) {
      context.read<CommissionRecordsCubit>().changeDateType(_tabController.index);
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) => Scaffold(
        appBar: _buildAppBar(),
        body: _buildBody(),
      );

  PreferredSizeWidget _buildAppBar() => AppBar(
        leading: InkWell(
          onTap: () {
            sl<NavigatorService>().unFocus();
            sl<NavigatorService>().pop();
          },
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 14.gw, vertical: 14.gw),
            child: Image(
              image: const AssetImage("assets/images/toolBar/icon_toolBar_back.png"),
              height: 20.gw,
              width: 20.gw,
            ),
          ),
        ),
        centerTitle: true,
        title: BlocSelector<CommissionRecordsCubit, CommissionRecordsState, Enum>(
          selector: (state) => state.paymentTabIndex,
          builder: (context, state) => TabComponent(
            label1: '投注',
            label2: '充值',
            selectedIndex: state.index,
            selectTab: context.read<CommissionRecordsCubit>().changePaymentTabIndex,
          ),
        ),
      );

  Widget _buildBody() => Padding(
        padding: EdgeInsets.symmetric(
          horizontal: 12.gw,
        ),
        child: TabBarPromotion(
          tabController: _tabController,
          tabs: _buildTabs(),
          children: [
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: List.generate(
                  3,
                  (_) => CommissionRecordsContent(),
                ),
              ),
            ),
          ],
        ),
      );

  List<Tab> _buildTabs() => [
        Tab(text: 'yesterday'.tr()),
        Tab(text: 'this_month'.tr()),
        Tab(text: 'all_time'.tr()),
      ];
}

// Content Widget
class CommissionRecordsContent extends StatelessWidget {
  CommissionRecordsContent({super.key});
  final TextEditingController _controller = TextEditingController();
  final RefreshController refreshController = RefreshController(initialRefresh: false);

  @override
  Widget build(BuildContext context) => BlocBuilder<CommissionRecordsCubit, CommissionRecordsState>(
        builder: (context, state) => Column(
          children: [
            SizedBox(height: 10.gw),
            SearchField(
                controller: _controller,
                hintText: 'enter_agent_id'.tr(),
                onSearch: () => context
                    .read<CommissionRecordsCubit>()
                    .fetchCommissionRecords(childUserId: (_controller.text.trim()))),
            SizedBox(height: 14.gw),
            _buildTableHeader(state.paymentTabIndex),
            if (state.commissionDetailsNetState == NetState.emptyDataState)
              Center(
                child: Padding(
                  padding: EdgeInsets.symmetric(vertical: 50.gw),
                  child: Text('no_data_found'.tr()),
                ),
              ),
            if (state.commissionDetailsNetState == NetState.dataSuccessState)
              Expanded(
                child: _RecordsList(
                  records: state.commissionDetailsEntity?.records ?? [],
                  refreshController: refreshController,
                ),
              ),
          ],
        ),
      );

  Widget _buildTableHeader(TeamType type) => TabHeader(
        width: 90.gw,
        children: type == TeamType.bet ? _buildBetHeaders() : _buildRechargeHeaders(),
      );

  List<TableHeaderCell> _buildBetHeaders() => [
        _buildHeaderCell('subordinate', 3),
        _buildHeaderCell('bet_amount', 4),
        _buildHeaderCell('bet_commission', 3),
        _buildHeaderCell('time', 4),
      ];

  List<TableHeaderCell> _buildRechargeHeaders() => [
        _buildHeaderCell('subordinate', 3),
        _buildHeaderCell('recharge_amount', 4),
        _buildHeaderCell('recharge_commission_amount', 3),
        _buildHeaderCell('time', 4),
      ];

  TableHeaderCell _buildHeaderCell(String text, int flex) => TableHeaderCell(
        text: text.tr(),
        flex: flex,
        isBold: true,
      );
}

// Records List
class _RecordsList extends StatelessWidget {
  final List<CommissionDetailsRecords> records;
  final RefreshController refreshController;

  const _RecordsList({required this.records, required this.refreshController});

  void _onRefresh(BuildContext context) {
    context.read<CommissionRecordsCubit>().updatePageNo(1);
    context.read<CommissionRecordsCubit>().fetchCommissionRecords();
    refreshController.resetNoData();
    refreshController.refreshCompleted();
  }

  void _onLoading(BuildContext context) async {
    final hasMore = await context.read<CommissionRecordsCubit>().loadMoreCommissionRecords();
    if (hasMore) {
      refreshController.loadComplete();
    } else {
      refreshController.loadNoData();
    }
  }

  @override
  Widget build(BuildContext context) => AnimationLimiter(
        child: CommonRefresher(
          onRefresh: () => _onRefresh(context),
          onLoading: () => _onLoading(context),
          refreshController: refreshController,
          enablePullDown: true,
          enablePullUp: true,
          listWidget: ListView.builder(
            itemCount: records.length,
            itemBuilder: (context, index) => AnimationConfiguration.staggeredList(
              position: index,
              duration: const Duration(milliseconds: 375),
              child: ScaleAnimation(
                child: _RecordItem(record: records[index]),
              ),
            ),
          ),
        ),
      );
}

// Record Item
class _RecordItem extends StatelessWidget {
  final CommissionDetailsRecords record;

  const _RecordItem({required this.record});

  @override
  Widget build(BuildContext context) => SizedBox(
        height: 34.gw,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            _buildCell(context, record.subUserNo?.maskString ?? '', 3),
            _buildCell(context, record.amount?.formattedMoney ?? '', 4),
            _buildCell(context, record.commissionAmount?.formattedMoney ?? '', 3),
            _buildCell(context, _formatDate(record.belongDate), 4),
          ],
        ),
      );

  Widget _buildCell(BuildContext context, String text, int flex) => Expanded(
        flex: flex,
        child: Text(
          text,
          textAlign: TextAlign.center,
          style: Theme.of(context).textTheme.bodyMedium,
        ),
      );

  String _formatDate(String? date) => date != null ? DateTime.parse(date).toIso8601String().split('T').first : '';
}
