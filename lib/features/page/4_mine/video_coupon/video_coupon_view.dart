import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wd/core/base/base_stateful_page.dart';
import 'package:wd/core/models/apis/time_fetcher.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/singletons/user_state.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/global_config.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/app_image.dart';
import 'package:wd/shared/widgets/common_button.dart';
import 'package:wd/shared/widgets/common_card.dart';
import 'package:wd/shared/widgets/common_textfield.dart';
import 'package:wd/shared/widgets/header_content_card.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';

import 'video_coupon_cubit.dart';

class VideoCouponPage extends BasePage {
  const VideoCouponPage({super.key});

  @override
  BasePageState<BasePage> getState() => _VideoCouponPageState();
}

class _VideoCouponPageState extends BasePageState {
  String? ruleImageUrl;
  final textController = TextEditingController();

  @override
  void initState() {
    pageTitle = "redeem_code".tr();
    fetchRuleImage();
    super.initState();
  }

  fetchRuleImage() async {
    ruleImageUrl = await GlobalConfig().getConfigValueByKey("video_watch_rule") ?? '';
    setState(() {});
  }

  @override
  Widget buildPage(BuildContext context) {
    final cubit = BlocProvider.of<VideoCouponCubit>(context);
    return Scaffold(
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 10.gw),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(height: 12.gw),
                    _buildCouponCard(),
                    SizedBox(height: 12.gw),
                    CommonCard(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "redeem_code_hint".tr(),
                            style: context.textTheme.btnSecondary.fs16,
                          ),
                          SizedBox(height: 12.gw),
                          _buildInputField(),
                          SizedBox(height: 12.gw),
                          CommonButton(
                            title: "redeemNow".tr(),
                            onPressed: () {
                              sl<NavigatorService>().unFocus();
                              cubit.userOnClickSubmit(textController);
                            },
                          ),
                        ],
                      ),
                    ),
                    HeaderContentCard(
                      header: Text(
                        "importantRules".tr(),
                        style: context.textTheme.primary.fs16,
                      ),
                      content: Column(
                        children: [
                          if (ruleImageUrl != null) ...[
                            ClipRRect(
                              borderRadius: BorderRadius.only(
                                bottomLeft: Radius.circular(6.gw),
                                bottomRight: Radius.circular(6.gw),
                              ),
                              child: AppImage(
                                imageUrl: ruleImageUrl!,
                                fit: BoxFit.fitWidth,
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                    SizedBox(height: 12.gw),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCouponCard() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.fromLTRB(20.gw, 20.gw, 30.gw, 20.gw),
      decoration: const BoxDecoration(
        image: DecorationImage(
          image: AssetImage("assets/images/mine/video_coupon/bg.png"),
          fit: BoxFit.fill,
        ),
      ),
      child: BlocSelector<UserCubit, UserState, ({String? dayStr, String expiredDateStr})>(
        selector: (state) => (
          dayStr: state.videoVipInfo?.days.toString(),
          expiredDateStr: state.videoVipInfo?.expiredDate ?? '',
        ),
        builder: (context, model) {
          return Stack(
            clipBehavior: Clip.none,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  AneText(
                    'remain_days'.tr(),
                    style: TextStyle(fontSize: 14.fs, color: Colors.white),
                  ),
                  SizedBox(height: 4.gw),
                  RichText(
                    text: TextSpan(
                      children: [
                        TextSpan(
                          text: model.dayStr ?? '0',
                          // style: TextStyle(fontSize: 32.fs, fontWeight: FontWeight.bold, color: Colors.white),
                          style:
                              context.textTheme.btnPrimary.fs30.w700.copyWith(color: context.colorTheme.btnBgPrimary),
                        ),
                        WidgetSpan(
                          alignment: PlaceholderAlignment.bottom,
                          child: Text(
                            ' ${'days'.tr()}',
                            // style: TextStyle(fontSize: 14.fs, fontWeight: FontWeight.bold, color: Colors.white),\
                            style: context.textTheme.btnPrimary.w700.copyWith(color: context.colorTheme.btnBgPrimary),
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 14.gw),
                  Row(
                    children: [
                      const Icon(Icons.timer_outlined, color: Colors.white),
                      SizedBox(width: 8.gw),
                      AneText(
                        '${'expiry'.tr()}: ${TimeFetcher.formatDateToDDMMYYYY(model.expiredDateStr)}',
                        // style: TextStyle(fontSize: 14.fs, color: Colors.white),
                        style: context.textTheme.btnSecondary.w700,
                      ),
                    ],
                  ),
                  if (model.dayStr != null && (int.tryParse(model.dayStr!) ?? 0) > 0)
                    Row(
                      children: [
                        const Icon(Icons.timer_outlined, color: Colors.white),
                        SizedBox(width: 8.gw),
                        Text(
                          '${'expiry'.tr()}: ${TimeFetcher.formatDateToDDMMYYYY(model.expiredDateStr)}',
                          // style: TextStyle(fontSize: 14.fs, color: Colors.white),
                          style: context.textTheme.btnPrimary.w700.copyWith(color: context.colorTheme.btnBgPrimary),
                        ),
                      ],
                    ),
                ],
              ),
              Positioned(
                right: -20,
                bottom: -10,
                child: Image.asset("assets/images/mine/video_coupon/coins.png", width: 247.gw, height: 90.gw),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildInputField() {
    return CommonTextField(
      controller: textController,
      hintText: 'enter_redeem_code'.tr(),
    );
  }
}
