import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/base/base_stateful_page.dart';
import 'package:wd/core/base/common_refresher.dart';
import 'package:wd/core/constants/assets.dart';
import 'package:wd/core/models/entities/notifications_response_entity.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:wd/shared/widgets/easy_loading.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import 'notification_cubit.dart';
import 'notification_state.dart';

class NotificationsListView extends BasePage {
  const NotificationsListView({super.key});

  @override
  BasePageState<BasePage> getState() => _NotificationsListViewState();
}

class _NotificationsListViewState extends BasePageState<NotificationsListView> {
  /// 刷新组件控制器
  final RefreshController refreshController = RefreshController(initialRefresh: false);

  @override
  void initState() {
    pageTitle = 'notifications'.tr();
    super.initState();
  }

  @override
  Widget right() {
    final unreadCount = context.watch<NotificationsCubit>().state.unreadCount;
    return GestureDetector(
      onTap: () {
        if (unreadCount > 0) {
          context.read<NotificationsCubit>().markAllAsRead();
        } else {
          GSEasyLoading.showToast('暂无未读消息');
        }
      },
      child: Row(
        children: [
          SvgPicture.asset(
            Assets.iconMarkAllAsRead,
            width: 19.gw,
            height: 19.gw,
            fit: BoxFit.contain,
          ),
          SizedBox(width: 4.gw),
          Text(
            '全部已读',
            style: TextStyle(
              fontSize: 14.fs,
              color: const Color(0xff6A7391),
            ),
          ),
        ],
      ),
    );
  }

  _getData({required bool reset, required bool updateCount}) {
    context.read<NotificationsCubit>().fetchNotifications(updateCount: updateCount, reset: reset);
  }

  Widget mainPageWidget(NotificationState state) {
    return Column(children: [
      if (state.netState == NetState.emptyDataState) ...[
        Expanded(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              emptyWidget('暂无数据'),
            ],
          ),
        )
      ],
      if (state.netState == NetState.dataSuccessState) ...[
        Expanded(
            child: AnimationLimiter(
          child: CommonRefresher(
            enablePullDown: true,
            enablePullUp: true,
            refreshController: refreshController,
            onRefresh: () => _getData(reset: true, updateCount: true),
            onLoading: _onLoading,
            listWidget: ListView.separated(
                padding: EdgeInsets.only(top: 10.gw),
                itemBuilder: (context, index) {
                  if (state.dataList!.length <= index) return const SizedBox();
                  final model = state.dataList![index];
                  return AnimationConfiguration.staggeredList(
                    position: index,
                    duration: const Duration(milliseconds: 375),
                    child: SlideAnimation(
                      horizontalOffset: 50.0,
                      child: FadeInAnimation(
                        child: NotificationListCell(
                          notification: model,
                          onTap: () {
                            context.read<NotificationsCubit>().markAsRead(model.id);
                          },
                        ),
                      ),
                    ),
                  );
                },
                separatorBuilder: (_, __) => SizedBox(height: 10.gw),
                itemCount: state.dataList!.length),
          ),
        )),
      ],
    ]);
  }

  void _listener(BuildContext context, NotificationState state) {
    refreshController.refreshCompleted();
    refreshController.loadComplete();
    if (state.isNoMoreDataState == true) {
      refreshController.loadNoData();
    }
  }

  void _onLoading() {
    context.read<NotificationsCubit>().updatePageNoToNext();
    context.read<NotificationsCubit>().fetchNotifications();
  }

  @override
  Widget buildPage(BuildContext context) {
    return BlocConsumer<NotificationsCubit, NotificationState>(
      listener: _listener,
      builder: (context, state) {
        return resultWidget(
          state,
          (baseState, context) => mainPageWidget(state),
          refreshMethod: () {
            _getData(reset: true, updateCount: true);
          },
        );
      },
    );
  }
}

class NotificationListCell extends StatelessWidget {
  final NotificationsRecords notification;
  final VoidCallback? onTap;

  const NotificationListCell({
    super.key,
    required this.notification,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final dateString = notification.createTime;
    final notificationDateFormatted = dateString.toDateTime();

    return GestureDetector(
      onTap: () {
        if (notification.siteMessageRead == 0 && onTap != null) {
          onTap!();
        }
      },
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 2),
        padding: const EdgeInsets.all(10),
        color: notification.siteMessageRead == 1
            ? Theme.of(context).disabledColor.withOpacity(.1)
            : Theme.of(context).colorScheme.surface,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              width: 30.gw,
              alignment: Alignment.center,
              child: Image.asset(
                "xx/xx",
              ),
            ),
            SizedBox(width: 16.gw),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Text(
                          notification.siteMessageTitle,
                          style: Theme.of(context).textTheme.titleMedium!.copyWith(color: Colors.black),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      Text(
                        notificationDateFormatted.formattedDate,
                        style: Theme.of(context).textTheme.labelMedium,
                      ),
                    ],
                  ),
                  const SizedBox(height: 2),
                  Text(
                    notification.siteMessageContent,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
