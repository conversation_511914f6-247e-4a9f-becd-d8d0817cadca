import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/base/net_error_widget.dart';
import 'package:wd/core/models/entities/top_up_list_entity.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:wd/shared/widgets/app_image.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/shared/widgets/button/gradient_button.dart';
import 'package:wd/shared/widgets/common_button.dart';
import 'package:wd/shared/widgets/dialog/usdt_protocol_dialog.dart';
import 'package:wd/shared/widgets/header_content_card.dart';
import 'package:wd/shared/widgets/transact/top_up/top_up_textfield.dart';
import 'package:wd/shared/widgets/transact/top_up/top_up_way_cell.dart';
import 'package:wd/shared/widgets/transact/transact_sel_button.dart';
import 'package:wd/shared/widgets/transact/transact_section_widget.dart';
import 'package:wd/shared/widgets/transact/transact_title_view.dart';
import 'package:keyboard_avoider/keyboard_avoider.dart';
import 'package:keyboard_dismisser/keyboard_dismisser.dart';
import 'topup_cubit.dart';

class TopupPage extends StatefulWidget {
  const TopupPage({super.key});

  @override
  _TopupPageState createState() => _TopupPageState();
}

class _TopupPageState extends State<TopupPage> {
  final selectedBorderColor = const Color(0xffCDB296);
  final TextEditingController _textController = TextEditingController();

  @override
  void initState() {
    _textController.addListener(() {
      context.read<TopupCubit>().updateInput(_textController.text);
    });
    super.initState();
  }

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<TopupCubit, TopupState>(
      builder: (context, state) {
        if (state.netState == NetState.initializeState) {
          return const Center(child: CircularProgressIndicator());
        } else if (state.netState == NetState.emptyDataState) {
          return NetErrorWidget(
            title: "无可用充值渠道",
            refreshMethod: () => context.read<TopupCubit>().fetchTopUpList(),
          );
        }

        return KeyboardAvoider(
          child: Container(
            color: Theme.of(context).scaffoldBackgroundColor,
            child: SingleChildScrollView(
              // padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
              child: AnimationLimiter(
                child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: AnimationConfiguration.toStaggeredList(
                      duration: const Duration(milliseconds: 375),
                      childAnimationBuilder: (widget) => SlideAnimation(
                        horizontalOffset: 50.0,
                        child: FadeInAnimation(
                          child: widget,
                        ),
                      ),
                      children: [
                        _getTopUpSection(),
                        _buildPaymentChannelWidget(),
                        _buildAmountWidget(),
                        _buildCommitButton(),
                        _getTipsWidget(),
                        SizedBox(height: 16.gw),
                      ],
                    )),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _getTopUpSection() {
    final cubit = context.read<TopupCubit>();
    final state = cubit.state;

    return TransactSectionWidget(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const TransactTitleView(type: TransactTitleViewType.paymentMethod),
          GridView.builder(
            shrinkWrap: true,
            padding: EdgeInsets.fromLTRB(16.gw, 20.gw, 16.gw, 16.gw),
            physics: const NeverScrollableScrollPhysics(),
            // 禁用 GridView 的滚动
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              crossAxisSpacing: 12.gw,
              mainAxisSpacing: 8.gw,
              childAspectRatio: 1,
            ),
            itemCount: state.topUpList.length,
            itemBuilder: (context, index) {
              final item = state.topUpList[index];
              final isSelected = item == state.selectedPayWay;
              return _getPayWayItem(context, item, isSelected);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentChannelWidget() {
    final cubit = context.read<TopupCubit>();
    final state = cubit.state;
    return TransactSectionWidget(
        child: Column(
      children: [
        if (state.selectedPayWay != null) ...[
          TransactTitleView(
            type: TransactTitleViewType.selectChannel,
            child: state.selectedPayType?.redirectWalletLobby == true
                ? Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      InkWell(
                        onTap: () => cubit.fetchThirdPartyWalletBalance(state.selectedPayType),
                        child: Text(
                          "${state.selectedPayWay?.payWayName}余额: ${state.selectedPayType?.balance ?? "--"}元",
                          style:
                              TextStyle(fontSize: 14.fs, color: const Color(0xffCDB296), fontWeight: FontWeight.w500),
                        ),
                      ),
                      SizedBox(width: 5.gw),
                      GradientButton(
                          title: "购币",
                          width: 56.gw,
                          height: 22.gw,
                          textStyle:
                              TextStyle(color: const Color(0xff7E6245), fontSize: 12.fs, fontWeight: FontWeight.w600),
                          onPressed: () => cubit.goToThirdPartyWalletLobby(state.selectedPayType)),
                    ],
                  )

                /// 需求：暂时注释
                // : state.selectedPayWay!.payWayCode == "USDT"
                //     ? InkWell(
                //         onTap: () => const USDTProtocolDialog().show(),
                //         child: Row(
                //           mainAxisAlignment: MainAxisAlignment.end,
                //           children: [
                //             Icon(
                //               Icons.error_rounded,
                //               size: 16.gw,
                //               color: const Color(0xff808C9F),
                //             ),
                //             SizedBox(width: 4.gw),
                //             Text(
                //               "钱包协议",
                //               style: Theme.of(context).textTheme.titleMedium?.copyWith(color: const Color(0xff808C9F)),
                //             ),
                //           ],
                //         ),
                //       )
                : Container(),
          ),
          GridView.builder(
            shrinkWrap: true,
            padding: EdgeInsets.fromLTRB(16.gw, 20.gw, 16.gw, 16.gw),
            physics: const NeverScrollableScrollPhysics(),
            // 禁用 GridView 的滚动
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              crossAxisSpacing: 8.gw,
              mainAxisSpacing: 8.gw,
              childAspectRatio: 117 / 50,
            ),
            itemCount: state.selectedPayWay!.payTypeList.length,
            itemBuilder: (context, index) {
              final payType = state.selectedPayWay!.payTypeList[index];
              final isSelected = payType == state.selectedPayType;
              return getPayTypeItem(context, payType, isSelected);
            },
          ),
        ],
        if (state.giftTips != null && state.giftTips! > 0) ...[
          Padding(
            padding: const EdgeInsets.only(left: 5.0),
            child: Text(
              "充值送${state.giftTips}%",
              style: Theme.of(context).textTheme.titleMedium?.copyWith(color: Theme.of(context).primaryColor),
            ),
          ),
          SizedBox(height: 5.gw),
        ],
      ],
    ));
  }

  Widget _buildAmountWidget() {
    final cubit = context.read<TopupCubit>();
    final state = cubit.state;
    TopUpListPayTypeList? selectPayType = state.selectedPayType;
    return TransactSectionWidget(
        child: Column(
      children: [
        TransactTitleView(
          type: TransactTitleViewType.depositAmount,
          subTitle: selectPayType != null
              ? "${'single_transaction_limit:'.tr()}${selectPayType.amountMinLimit.removeZeros}-${selectPayType.amountMaxLimit.removeZeros}"
              : null,
        ),
        if (selectPayType != null && selectPayType.fixedAmount) ...[
          GridView.builder(
            shrinkWrap: true,
            padding: EdgeInsets.fromLTRB(16.gw, 20.gw, 16.gw, 16.gw),
            physics: const NeverScrollableScrollPhysics(),
            // 禁用 GridView 的滚动
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              crossAxisSpacing: 12.gw,
              mainAxisSpacing: 12.gw,
              childAspectRatio: 122 / 73,
            ),
            itemCount: selectPayType.amountList.split(",").length,
            itemBuilder: (context, index) {
              final amountList = selectPayType.amountList.split(",");
              final money = amountList[index];
              final isSelected = state.input == money;
              return getFixedMoneyItem(context, money, isSelected);
            },
          ),
        ] else ...[
          SizedBox(height: 10.gw),
          Padding(
            padding: EdgeInsets.fromLTRB(16.gw, 20.gw, 16.gw, 16.gw),
            child: TopUpTextField(
              controller: _textController,
              hintText: 'select_or_input_amount'.tr(),
              maxLimit: state.selectedPayType?.amountMaxLimit ?? 0,
              rates: state.selectedPayWay?.exchangeRate,
              onChanged: (value) {
                context.read<TopupCubit>().updateInput(value);
              },
              enabled: state.isInputEnabled,
            ),
          ),
          SizedBox(height: 20.gw),
        ],
      ],
    ));
  }

  Widget _buildCommitButton() {
    return Container(
      margin: EdgeInsets.fromLTRB(20.gw, 16.gw, 20.gw, 0),
      child: CommonButton(
        height: 42.gw,
        title: "submit".tr(),
        onPressed: () => context.read<TopupCubit>().submitPayment(context),
      ),
    );
  }

  Widget _getPayWayItem(BuildContext context, TopUpListEntity topup, bool isSelected) {
    return GestureDetector(
      onTap: () {
        _textController.text = '';
        context.read<TopupCubit>().selectPayWay(topup);
      },
      child: TopUpWayCell(
          title: topup.payWayName,
          icon: topup.icon,
          isRecommended: topup.recommended,
          payWayTag: topup.payWayTag,
          isSelected: isSelected),
    );
  }

  Widget getPayTypeItem(BuildContext context, TopUpListPayTypeList payType, bool isSelected) {
    final cubit = context.read<TopupCubit>();
    return InkWell(
        onTap: () {
          _textController.text = '';
          cubit.selectPayType(payType);
          if (payType.redirectWalletLobby == true) {
            if (payType.balance == null) {
              cubit.fetchThirdPartyWalletBalance(payType);
            }
            if (payType.lobbyLink == null) {
              cubit.fetchThirdPartyWalletLobbyLink(payType);
            }
          }
        },
        child: TransactSelButton(
          title: payType.controllerTips,
          fontSize: 12.fs,
          isSelected: isSelected,
          payChannelTag: payType.payChannelTag,
        ));
  }

  Widget getFixedMoneyItem(BuildContext context, String money, bool isSelected) {
    return InkWell(
      onTap: () => context.read<TopupCubit>().updateInput(money),
      child: TransactSelButton(title: money, isSelected: isSelected, isAmount: true),
    );
  }

  _getTipsWidget() {
    const content = "Please follow the recharge order instructions to complete the payment. Do not duplicate, delay, or modify payments, or funds may not be credited. If you have questions, contact our 24/7 customer support!";
    return HeaderContentCard(
      margin: EdgeInsets.fromLTRB(20.gw, 16.gw, 20.gw, 0),
      header: Text('kindly_reminder'.tr(), style: context.textTheme.primary.fs16),
      content: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Text(content, style: context.textTheme.title),
      ),
    );

    const htmlStr = """
              <p>1.请按照<span style="color: rgb(255, 0, 0);">充值订单要求</span>进行支付，切勿重复/超时/篡改支付，否则无法到账，若有疑问联系24小时在线客服！</p>
              <p>2.建议使用<span style="color: rgb(0, 176, 80);">电子钱包</span>进行存取款，充值笔笔加赠彩金，安全快捷便利，无需担心风控问题，一站式解决您的资金问题！（在众多通道的选择中，总有一条是可以支付成功的，若遇到支付难题，请尝试更换通道，或者联系7*24小时在线客服！）。</p>
              <p>3.500元以上充值建议选择 USDT 充值通道，新会员以及要充值500元以下建议选择支付宝通道充值。</p>
                    """;



    return TransactSectionWidget(
      child: Column(
        children: [
          SizedBox(height: 9.gw),
          Text(
            "温馨提示",
            style: TextStyle(fontSize: 16.fs, color: selectedBorderColor),
          ),
          Image.asset("assets/images/transact/icon_tip_bottom.png", width: 198.gw, height: 8.8.gw),
          SizedBox(height: 10.gw),
          Html(data: htmlStr, style: {
            'p': Style(
                fontSize: FontSize.medium,
                // 设置字体大小
                color: const Color(0xff6A7391),
                lineHeight: LineHeight.number(1.2),
                // 设置行高
                margin: Margins.only(bottom: 3),
                padding: HtmlPaddings.zero),
          }),
          SizedBox(height: 10.gw),
        ],
      ),
    );
  }
}
