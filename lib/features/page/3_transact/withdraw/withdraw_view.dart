import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:flutter_svg/svg.dart';
import 'package:wd/core/config/bottom_nav_config.dart';
import 'package:wd/core/constants/constants.dart';
import 'package:wd/core/models/apis/transact.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/singletons/user_state.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/models/entities/withdraw_user_bank_list_entity.dart';
import 'package:wd/core/singletons/user_singleton.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:wd/core/utils/system_util.dart';
import 'package:wd/features/page/3_transact/withdraw/withdraw_state.dart';
import 'package:wd/features/page/4_mine/account_security/modify_pwd/modify_pwd_view.dart';
import 'package:wd/features/page/main/screens/main_screen_cubit.dart';
import 'package:wd/features/routers/app_router.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/app_image.dart';
import 'package:wd/shared/widgets/balance/balance_card.dart';
import 'package:wd/shared/widgets/card/metric_card.dart';
import 'package:wd/shared/widgets/common_button.dart';
import 'package:wd/shared/widgets/common_dialog.dart';
import 'package:wd/shared/widgets/easy_loading.dart';
import 'package:wd/shared/widgets/sheet/select_bank_card_sheet.dart';
import 'package:wd/shared/widgets/transact/top_up/top_up_textfield.dart';
import 'package:wd/shared/widgets/transact/transact_sel_button.dart';
import 'package:wd/shared/widgets/transact/transact_title_view.dart';
import 'package:wd/shared/widgets/transact/withdraw/withdraw_select_bank_cell.dart';
import 'package:wd/shared/widgets/transact/transact_section_widget.dart';
import 'package:keyboard_avoider/keyboard_avoider.dart';
import 'package:keyboard_dismisser/keyboard_dismisser.dart';

import '../../4_mine/account_security/payment_list/payment_list_state.dart';
import 'withdraw_cubit.dart';

class WithdrawPage extends StatefulWidget {
  const WithdrawPage({super.key});

  @override
  State<StatefulWidget> createState() => _WithdrawPageState();
}

class _WithdrawPageState extends State<WithdrawPage> with SingleTickerProviderStateMixin {
  final selectedBorderColor = const Color(0xffCDB296);
  final TextEditingController _textController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _textController.addListener(() {
      // 更新输入框内容
      context.read<WithdrawCubit>().updateInput(_textController.text);
    });
  }

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }

  Future<void> _handleWalletRefresh({int? userBankId}) async {
    if (userBankId == null) {
      // 获取用户余额
      await context.read<WithdrawCubit>().fetchUserBalance();
    } else {
      // 获取用户银行信息
      await context.read<WithdrawCubit>().fetchUserBankInfo(userBankInfoId: userBankId, showLoading: false);
    }
  }

  onClickAddBankCard(WithdrawState state) async {
    if (!sl<UserCubit>().state.isLogin) {
      return sl<MainScreenCubit>().selectedNavTypeChanged(BottomNavType.gameHome);
    }
    WithdrawType type = state.type;
    if (sl<UserCubit>().state.userInfo!.hasFundPwd) {
      if (type == WithdrawType.bankCard) {
        await sl<NavigatorService>().push(AppRouter.userWithdrawList, arguments: PaymentType.bankCard);
      } else if (type == WithdrawType.wallet) {
        await sl<NavigatorService>().push(AppRouter.userWithdrawList, arguments: PaymentType.wallet);
      } else if (type == WithdrawType.manualChannel) {
        if (state.currentSelManualChannel != null && state.currentSelManualChannel!.channelName == "USDT") {
          await sl<NavigatorService>().push(AppRouter.userWithdrawList, arguments: PaymentType.usdt);
        }
      }

      if (mounted) {
        GSEasyLoading.showLoading();
        // 刷新用户银行列表
        context.read<WithdrawCubit>().fetchUserBankList(type: type, showLoading: true);
        GSEasyLoading.dismiss();
      }
    } else {
      SystemUtil.showSetFundPwdDialog(context);
    }
  }

  onClickShowSelSheet({
    required WithdrawState state,
    required ValueChanged<WithdrawUserBankBrief> onBankCardSelected,
  }) {
    WithdrawType type = state.type;
    WithdrawUserBankInfoEntity? model;
    List<WithdrawUserBankBrief>? list;
    switch (type) {
      case WithdrawType.bankCard:
        model = state.currentSelBank;
        list = state.myBankCardList;
        break;
      case WithdrawType.wallet:
        model = state.currentSelWallet;
        list = state.myWalletList;
        break;
      case WithdrawType.manualChannel:
        model = state.currentSelUsdt;
        list = state.myUsdtList;
        break;
    }
    if (model == null || list == null || list.isEmpty) return;

    ShowSelectBankCardSheet(
      context,
      type: type,
      cardList: list,
      defaultBankCardId: model.userBankInfoId,
      onBankCardSelected: onBankCardSelected,
      onClickManage: () => onClickAddBankCard(state),
    );
  }

  onClickSubmit() {
    // 提交提现请求
    sl<NavigatorService>().unFocus();
    context.read<WithdrawCubit>().submitBankWithdraw(context, controller: _textController);
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<UserCubit, UserState>(
      listenWhen: (previous, current) => current.isLogin != previous.isLogin,
      listener: (context, state) {
        if (!state.isLogin) {
          context.read<WithdrawCubit>().resetData();
        }
      },
      child: BlocBuilder<WithdrawCubit, WithdrawState>(
        builder: (context, state) {
          WithdrawUserBankInfoEntity? currentSelModel =
              state.type == WithdrawType.bankCard ? state.currentSelBank : state.currentSelWallet;
          if (state.netState == NetState.initializeState) {
            return const Center(child: CircularProgressIndicator());
          }

          return KeyboardAvoider(
            child: SingleChildScrollView(
              // padding: EdgeInsets.only(
              //   bottom: MediaQuery.of(context).viewInsets.bottom,
              // ),
              child: AnimationLimiter(
                  child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: AnimationConfiguration.toStaggeredList(
                    duration: const Duration(milliseconds: 375),
                    childAnimationBuilder: (widget) => SlideAnimation(
                          horizontalOffset: 50.0,
                          child: FadeInAnimation(
                            child: widget,
                          ),
                        ),
                    children: [
                      SizedBox(height: 10.gw),
                      _buildBalanceSection(state, currentSelModel),
                      SizedBox(height: 10.gw),
                      _buildOperationSection(state, currentSelModel),
                      SizedBox(height: 10.gw),
                      if (state.type != WithdrawType.manualChannel && currentSelModel != null) ...[
                        _getTipsWidget(currentSelModel),
                        SizedBox(height: 10.gw),
                      ],
                    ]),
              )),
            ),
          );
        },
      ),
    );
  }

  Widget getWithdrawTypeItem(
      BuildContext context, String title, String imagePath, bool isSelected, WithdrawState state) {
    return GestureDetector(
      onTap: () {
        // 切换提现类型
        WithdrawType type = WithdrawType.bankCard;
        if (title == "钱包") {
          type = WithdrawType.wallet;
        } else if (title == "人工通道") {
          type = WithdrawType.manualChannel;
        }
        context.read<WithdrawCubit>().onChangeWithdrawType(type);
      },
      child: SizedBox(
        width: 113.gw,
        height: 56.gw,
        child: TransactSelButton(
            title: title,
            prefix: Image.asset(
              imagePath,
              width: 32.gw,
              height: 32.gw,
            ),
            isSelected: isSelected),
      ),
    );
  }

  _buildManualChannelItem(WithdrawManualChannelEntity model, isSelected) {
    return GestureDetector(
      onTap: () => context.read<WithdrawCubit>().onChangeCurrentManualChannel(model),
      child: SizedBox(
        width: 113.gw,
        height: 56.gw,
        child: TransactSelButton(
            title: model.channelName,
            prefix: AppImage(
              imageUrl: model.icon,
              width: 32.gw,
              height: 32.gw,
            ),
            isSelected: isSelected),
      ),
    );
  }

  _getSelBankOrWalletWidget({required WithdrawState state}) {
    String? btnTitle;
    WithdrawUserBankInfoEntity? model;
    switch (state.type) {
      case WithdrawType.bankCard:
        btnTitle = "bank_card".tr();
        model = state.currentSelBank;
        break;
      case WithdrawType.wallet:
        btnTitle = "wallet".tr();
        model = state.currentSelWallet;
        break;
      case WithdrawType.manualChannel:
        if (state.currentSelManualChannel != null && state.currentSelManualChannel!.channelName == "USDT") {
          btnTitle = "USDT";
          model = state.currentSelUsdt;
        }
        break;
    }

    if (model == null) {
      return Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.gw),
        child: CommonButton(
          title: '${'do_add'.tr()}$btnTitle '.tr(),
          onPressed: () => onClickAddBankCard(state),
        ),
      );
    } else {
      return GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () => onClickShowSelSheet(
            state: state,
            onBankCardSelected: (selModel) {
              if (selModel.id != model!.userBankInfoId) {
                // 更新选中的银行信息
                context.read<WithdrawCubit>().fetchUserBankInfo(userBankInfoId: selModel.id);
              }
            }),
        child: WithdrawSelectBankCell(
          type: state.type,
          model: WithdrawSelectBankCellViewModel.formWithdrawUserBankInfoEntity(model),
          showNext: true,
        ),
      );
    }
  }

  /// 余额模块
  _buildBalanceSection(WithdrawState state, WithdrawUserBankInfoEntity? currentSelModel) {
    return Container(
      margin: EdgeInsets.fromLTRB(20.gw, 16.gw, 20.gw, 0),
      height: 65.gw,
      child: Row(
        children: [
          /// 可提现余额
          Expanded(
              child: BlocSelector<UserCubit, UserState, int>(
            selector: (state) => state.videoVipInfo?.days ?? 0,
            builder: (context, days) {
              return MetricCard(
                'withdrawable'.tr(),
                iconPath: "assets/images/transact/v3/icon_withdrawable_balance.png",
                value: state.availableBalance.formattedMoney,
              );
            },
          )),
          SizedBox(width: 2.gw),

          /// 我的钱包
          Expanded(
              child: BalanceCard(
            title: 'account_balance'.tr(),
            onClickRefresh: () async {
              return await _handleWalletRefresh(userBankId: currentSelModel?.userBankInfoId);
            },
          )),
        ],
      ),
    );
  }

  _buildOperationSection(WithdrawState state, WithdrawUserBankInfoEntity? currentSelModel) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        TransactSectionWidget(
          child: Column(
            children: [
              const TransactTitleView(type: TransactTitleViewType.paymentMethod),

              /// 提现类型列表
              GridView.builder(
                shrinkWrap: true,
                padding: EdgeInsets.fromLTRB(16.gw, 20.gw, 16.gw, 16.gw),
                physics: const NeverScrollableScrollPhysics(),
                // 禁用 GridView 的滚动
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 3,
                  crossAxisSpacing: 8.gw,
                  mainAxisSpacing: 10.gw,
                  childAspectRatio: 113 / 56,
                ),
                itemCount: state.withdrawTypeList.length,
                itemBuilder: (context, index) {
                  final dic = state.withdrawTypeList[index];
                  final isSel = state.type.index == index;
                  return getWithdrawTypeItem(context, dic["title"]!, dic["image"]!, isSel, state);
                },
              ),
            ],
          ),
        ),
        SizedBox(height: 8.gw),
        TransactSectionWidget(
          child: Column(
            children: [
              TransactTitleView(
                type: TransactTitleViewType.selectAddress,
                child: state.type == WithdrawType.manualChannel && kChannel != "JS"
                    ? RichText(
                        textAlign: TextAlign.end,
                        text: TextSpan(
                          children: [
                            TextSpan(
                              text: '当前汇率:',
                              style: TextStyle(
                                color: const Color(0xffC2AA8E),
                                fontSize: 14.fs,
                              ),
                            ),
                            TextSpan(
                              text: ' ${state.currentSelManualChannel?.rate.toString() ?? "0"}',
                              style: TextStyle(
                                color: const Color(0xffC2AA8E),
                                fontSize: 14.fs,
                                fontFamily: 'DINCond-Bold',
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      )
                    : null,
              ),
              if (state.fetchingBankInfo &&
                  (state.type != WithdrawType.manualChannel ||
                      (state.type == WithdrawType.manualChannel && state.currentSelManualChannel == null))) ...[
                Container(
                  height: 80.gw,
                  alignment: Alignment.center,
                  child: SizedBox(
                      height: 18.gw,
                      width: 18.gw,
                      child: CircularProgressIndicator(
                        strokeWidth: 1.5,
                        color: selectedBorderColor,
                      )),
                ),
              ] else ...[
                if (state.type == WithdrawType.manualChannel) ...[
                  GridView.builder(
                    shrinkWrap: true,
                    padding: EdgeInsets.fromLTRB(0, 14.gw, 0, 20.gw),
                    physics: const NeverScrollableScrollPhysics(),
                    // 禁用 GridView 的滚动
                    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 3,
                      crossAxisSpacing: 8.gw,
                      mainAxisSpacing: 10.gw,
                      childAspectRatio: 113 / 56,
                    ),
                    itemCount: state.manualChannelList != null ? state.manualChannelList!.length : 0,
                    itemBuilder: (context, index) {
                      final model = state.manualChannelList![index];
                      final isSel = state.currentSelManualChannel == model;
                      return _buildManualChannelItem(model, isSel);
                    },
                  ),
                ] else ...[
                  SizedBox(height: 10.gw),
                  _getSelBankOrWalletWidget(state: state),
                  SizedBox(height: 20.gw),
                ],
              ],
            ],
          ),
        ),
        SizedBox(height: 1.gw),
        TransactSectionWidget(
          child: Column(
            children: [
              if (state.type == WithdrawType.manualChannel &&
                  state.currentSelManualChannel != null &&
                  state.currentSelManualChannel!.channelName == "USDT") ...[
                const TransactTitleView(type: TransactTitleViewType.selectAddress),
                if (state.fetchingBankInfo && (state.myUsdtList == null || state.currentSelUsdt == null)) ...[
                  Container(
                    height: 80.gw,
                    alignment: Alignment.center,
                    child: SizedBox(
                        height: 18.gw,
                        width: 18.gw,
                        child: CircularProgressIndicator(
                          strokeWidth: 1.5,
                          color: selectedBorderColor,
                        )),
                  ),
                ] else ...[
                  SizedBox(height: 10.gw),
                  _getSelBankOrWalletWidget(state: state),
                  SizedBox(height: 20.gw),
                ],
              ],
              const TransactTitleView(type: TransactTitleViewType.withdrawAmount),
              SizedBox(height: 10.gw),
      Padding(
        padding: EdgeInsets.fromLTRB(16.gw, 20.gw, 16.gw, 16.gw),
        child: TopUpTextField(
          controller: _textController,
          hintText: 'select_or_input_amount'.tr(),
                onChanged: (value) {
                  // 更新输入金额
                  context.read<WithdrawCubit>().updateInput(value);
                },
                maxLimit: state.availableBalance,
                rates: state.type == WithdrawType.manualChannel ? state.currentSelManualChannel?.rate : null,
                style: TextStyle(color: const Color(0xff3B416B), fontSize: 20.fs, fontWeight: FontWeight.w600),
              )),
              SizedBox(height: 20.gw),
            ],
          ),
        ),
        SizedBox(height: 20.gw),
        Container(
          margin: EdgeInsets.fromLTRB(20.gw, 16.gw, 20.gw, 0),
          child: CommonButton(
            height: 42.gw,
            title: "submit".tr(),
            showLoading: state.fetchingSubmit,
            enable: state.input.isNotEmpty && (currentSelModel != null || state.currentSelManualChannel != null),
            onPressed: () => onClickSubmit(),
          ),
        ),
      ],
    );
  }

  _getTipsWidget(WithdrawUserBankInfoEntity currentSelModel) {
    final htmlStr = """
          <p>1.提现处理时间为<span style="color: rgb(255, 0, 0);">00:00:00-23:59:59</span>,请在处理时间内操作。</p>
          <p>2.最低单笔申请提现金额<span style="color: rgb(0, 176, 80);">${currentSelModel.amountMinLimit.removeZeros}</span>,最高单笔提现金额<span style="color: rgb(0, 176, 80);">${currentSelModel.amountMaxLimit.removeZeros}。</span></p>
          <p>3.正常提现3-5分钟到账(如遇高峰期或跨行提现等因素将延迟30分钟内到账)。</p>
          ${currentSelModel.remark.isNotEmpty ? "<p>4.${currentSelModel.remark}</p>" : ""}
                    """;
    return TransactSectionWidget(
      child: Column(
        children: [
          SizedBox(height: 9.gw),
          Text(
            "温馨提示",
            style: TextStyle(fontSize: 16.fs, color: selectedBorderColor),
          ),
          Image.asset("assets/images/transact/icon_tip_bottom.png", width: 198.gw, height: 8.8.gw),
          SizedBox(height: 10.gw),
          Html(data: htmlStr, style: {
            'p': Style(
                fontSize: FontSize.medium,
                // 设置字体大小
                color: const Color(0xff6A7391),
                lineHeight: LineHeight.number(1.2),
                // 设置行高
                margin: Margins.only(bottom: 3),
                padding: HtmlPaddings.zero),
          }),
          SizedBox(height: 10.gw),
        ],
      ),
    );
  }
}
