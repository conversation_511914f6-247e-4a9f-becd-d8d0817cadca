import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/image_util.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/features/page/3_chat_plus/pages/chat_main/widget/chat_connect_state_loading.dart';
import 'package:wd/shared/mixin/hide_float_button_route_aware_mixin.dart';
import 'package:wd/shared/widgets/home/<USER>';
import 'package:wd/shared/widgets/text/gradient_text.dart';
import 'dart:ui' as ui;

import '../chat_contact/chat_contact_view.dart';
import '../chat_conversation/chat_conversation_view.dart';
import 'chat_main_cubit.dart';
import 'chat_main_state.dart';

class ChatMainPage extends StatefulWidget {
  @override
  State<StatefulWidget> createState() => _ChatMainPageState();
}

class _ChatMainPageState extends State<ChatMainPage>
    with SingleTickerProviderStateMixin, HideFloatButtonRouteAwareMixin {
  ui.Image? _tabIndicatorImage;
  late TabController _tabController;
  final tabList = ["消息", "通讯录"];

  @override
  void initState() {
    _loadTabIndicatorImage();

    // final cubit = context.read<TransactCubit>();
    _tabController = TabController(length: tabList.length, vsync: this)
      ..addListener(() {
        if (_tabController.indexIsChanging) return;
        if (_tabController.index != _tabController.previousIndex) {
          if (_tabController.index == 0) {
            // cubit.fetchTopUpListData();
          } else {
            // cubit.fetchWithdrawBankListData(showLoading: true);
          }
          // _pageController.jumpToPage(_tabController.index);
          setState(() {

          });
        }
      });


    super.initState();
  }

  _loadTabIndicatorImage() async {
    _tabIndicatorImage = await ImageUtil.loadImageAsset('assets/images/home/<USER>');
    setState(() {}); // 触发界面重新构建
  }

  Widget _buildTabItem({
    required String text,
    required bool isSelected,
  }) {
    var colors = const [Color(0xff6A7391), Color(0xff6A7391)];
    if (isSelected) colors = const [Color(0xffEACA9F), Color(0xffB9936D)];
    return GradientText(
      text: text,
      colors: colors,
      fontSize: 16.fs,
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ChatMainCubit, ChatMainState>(
      builder: (context, state) {
        return Scaffold(
          resizeToAvoidBottomInset: false,
          appBar: AppBar(
            elevation: 0.1,
            shadowColor: const Color(0x80B6B6B6),
            backgroundColor: Colors.white,
            centerTitle: true,
            title: TabBar(
              tabAlignment: TabAlignment.center,
              padding: EdgeInsets.zero,
              controller: _tabController,
              indicatorPadding: EdgeInsets.zero,
              labelPadding: EdgeInsets.symmetric(horizontal: 15.gw),
              indicator:
                  _tabIndicatorImage != null ? HomeTabIndicator(image: _tabIndicatorImage!, radius: 10.gw) : null,
              tabs: tabList.map((str) {
                final isSelected = _tabController.index == tabList.indexOf(str);
                return _buildTabItem(text: str, isSelected: isSelected);
              }).toList(),
            ),
            leading: IconButton(
              onPressed: () => Navigator.of(context).pop(),
              icon: const Image(
                image: AssetImage("assets/images/toolBar/icon_toolBar_back.png"),
                height: 30,
                width: 30,
              ),
            ),
            actions: [

              ChatConnectStateLoading(isLoading: state.tencentConnectStatus != ChatConnectStatus.success),

              // if (state.tencentConnectStatus == ChatConnectStatus.loading)
              //   Text("loading"),
              // if (state.tencentConnectStatus == ChatConnectStatus.idle)
              //   Text("idle"),
              // if (state.tencentConnectStatus == ChatConnectStatus.success)
              //   Text("success"),
              // if (state.tencentConnectStatus == ChatConnectStatus.failed)
              //   Text("failed"),
            ],
          ),
          body: TabBarView(
            controller: _tabController,
            physics: const NeverScrollableScrollPhysics(),
            children: [ChatConversationPage(), ChatContactPage()],
          ),
        );
      },
    );
  }
}
