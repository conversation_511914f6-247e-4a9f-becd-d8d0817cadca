import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/base/net_error_widget.dart';
import 'package:wd/core/constants/assets.dart';
import 'package:wd/core/constants/constants.dart';
import 'package:wd/core/models/entities/bonus_pool_entity.dart';
import 'package:wd/core/models/entities/game_notice_entity.dart';
import 'package:wd/core/models/entities/game_v2_entity.dart';
import 'package:wd/core/models/entities/home_banner_entity.dart';
import 'package:wd/core/models/view_models/popular_section_view_model.dart';

import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/auth_util.dart';
import 'package:wd/core/utils/connectivity_util.dart';
import 'package:wd/core/utils/game_home_util.dart';
import 'package:wd/core/utils/game_util.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/features/page/0_home/home_drawer/home_drawer_cubit.dart';
import 'package:wd/features/page/0_home/home_drawer/home_drawer_view.dart';
import 'package:wd/features/page/0_tiktok/video_home_cubit.dart';
import 'package:wd/features/page/0_tiktok/video_home_state.dart';
import 'package:wd/features/page/main/screens/main_screen_cubit.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/delay_button.dart';
import 'package:wd/shared/widgets/empty_app_bar.dart';
import 'package:wd/shared/widgets/game/game_home_banner_widget.dart';
import 'package:wd/shared/widgets/game/game_home_jackpot_widget.dart';
import 'package:wd/shared/widgets/game/game_home_marquee_text_widget.dart';
import 'package:wd/shared/widgets/game/game_home_section_title.dart';
import 'package:wd/shared/widgets/game/game_home_top_bar.dart';
import 'package:wd/shared/widgets/game/game_home_user_info_widget.dart';
import 'package:wd/shared/widgets/game/popular/game_home_popular_section.dart';
import 'package:wd/shared/widgets/lobby/lobby_redpacket_dialog.dart';
import 'package:wd/shared/widgets/platform/platform_list_cell.dart';
import 'package:wd/shared/widgets/platform/platform_list_cell_horizon.dart';
import 'package:wd/shared/widgets/platform/platform_tab_cell.dart';
import 'package:wd/shared/widgets/scroll_view/linked_scroll_controller/linked_scroll_controller.dart';
import 'package:wd/shared/widgets/scroll_view/linked_scroll_controller/nested_scroll_coordinator.dart';

import 'game_home_cubit.dart';
import 'game_home_state.dart';

class GameHomePage extends StatefulWidget {
  const GameHomePage({super.key});

  @override
  State<StatefulWidget> createState() => _GameHomePageState();
}

class _GameHomePageState extends State<GameHomePage> with TickerProviderStateMixin {
  final paddingH = 20.gw;
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  ///页面滑动协调器
  final NestedScrollCoordinator _viewCoordinator = NestedScrollCoordinator();

  LinkedScrollController? _scrollController;
  LinkedScrollController? _subScrollController;

  late AnimationController _balanceRefreshController;
  late AnimationController _scaleController;
  late AnimationController _slideController;
  static const Duration _animDuration = Duration(milliseconds: 250);
  static const Duration _scaleDuration = Duration(milliseconds: 200);

  int? _animatingIndex;
  bool _isProcessing = false;

  // Add these new variables
  bool _isScrolling = false;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _setupInitialData();
    context.read<GameHomeCubit>().fetchRecentlyPlayList();
    context.read<GameHomeCubit>().fetchData();
    context.read<GameHomeCubit>().startBonusPoolPolling();
  }

  void _initializeControllers() {
    // _gridScrollController = ScrollController();
    _balanceRefreshController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 1),
    );
    _scaleController = AnimationController(
      vsync: this,
      duration: _scaleDuration,
    );
    _slideController = AnimationController(
      vsync: this,
      duration: _animDuration,
    )..value = 1.0;
  }

  void _setupInitialData() {
    if (mounted) {
      sl<MainScreenCubit>().fetchNoticeDialogListData();
    }
  }

  bool hadListener = false;

  void _setupScrollListener() {
    if (hadListener) return;
    hadListener = true;
    _subScrollController?.addListener(() {
      if (!_isScrolling && mounted) {
        _gameListViewOnScroll();
      }
    });
  }

  void _gameListViewOnScroll() {
    double offset = _subScrollController?.offset ?? 0;

    double accumulatedHeight = 0;

    final homeCubit = context.read<GameHomeCubit>();
    final sectionHeights = homeCubit.state.sectionHeights;
    for (int i = 0; i < sectionHeights.length; i++) {
      if (homeCubit.state.gameTypeList[i].name == "热门") {
        accumulatedHeight += _getPopularSectionHeight();
      } else {
        accumulatedHeight += sectionHeights[i];
      }
      if (offset < accumulatedHeight) {
        context.read<GameHomeCubit>().onChangeCurrentTabIndex(i);
        break;
      }
    }
  }

  _getPopularSectionHeight() {
    if (mounted) {
      final gameCubit = context.read<GameHomeCubit>();
      double height = 0;
      switch (gameCubit.state.currentPopularIndex) {
        case 0:
          height = gameCubit.state.popularModel?.sectionHeight ?? 200.gw;
          break;
        case 1:
          height = gameCubit.state.recentlyModel?.sectionHeight ?? 200.gw;
          break;
        case 2:
          height = gameCubit.state.favModel?.sectionHeight ?? 200.gw;
          break;
      }
      return height;
    }
    return 0;
  }

  @override
  void dispose() {
    // _gridScrollController.dispose();
    _balanceRefreshController.dispose();
    _scaleController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  _onTabCellTap(int index) async {
    if (_isScrolling) return;
    setState(() {
      _animatingIndex = index;
      _isScrolling = true;
    });
    context.read<GameHomeCubit>().onChangeCurrentTabIndex(index);

    // Handle animation first
    _scaleController.forward().then((_) {
      _scaleController.reverse().then((_) {
        if (mounted) {
          setState(() {
            _animatingIndex = null;
          });
        }
      });
    });

    double targetOffset = 0;

    final cubit = context.read<GameHomeCubit>();
    for (int i = 0; i < index; i++) {
      if (cubit.state.gameTypeList[i].isHot) {
        targetOffset += _getPopularSectionHeight();
      } else {
        targetOffset += cubit.state.sectionHeights[i];
      }
    }

    await _subScrollController?.animateTo(
      targetOffset,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );

    if (mounted) {
      context.read<GameHomeCubit>().onChangeCurrentTabIndex(index);
      setState(() {
        _isScrolling = false;
      });
    }
  }

  Future<void> _handleRedPacketTap() async {
    if (_isProcessing || !mounted) return;

    _isProcessing = true;
    try {
      await AuthUtil.checkIfLogin(() async {
        final isEligible = await context.read<GameHomeCubit>().checkRedPacketEligibility();
        if (isEligible && mounted) {
          LobbyRedPacketDialog().show(context);
        }
      });
    } finally {
      _isProcessing = false;
    }
  }

  Widget _buildGridView() {
    return BlocSelector<GameHomeCubit, GameHomeState, List<GameTypeV2>>(
      selector: (state) => state.gameTypeList,
      builder: (context, gameTypeList) {
        return SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(0, 0.15),
            end: Offset.zero,
          ).animate(CurvedAnimation(
            parent: _slideController,
            curve: Curves.easeOutCubic,
          )),
          child: ListView.builder(
            shrinkWrap: true,
            controller: _subScrollController,
            padding: EdgeInsets.only(top: 5.gw, bottom: 10.gw, right: paddingH),
            itemCount: gameTypeList.length,
            itemBuilder: (context, sectionIndex) {
              final gameType = gameTypeList[sectionIndex];
              if (gameType.type == 3) {
                return BlocSelector<
                    GameHomeCubit,
                    GameHomeState,
                    ({
                      PopularSectionViewModel? popularModel,
                      PopularSectionViewModel? recentlyModel,
                      PopularSectionViewModel? favModel
                    })>(
                  selector: (state) =>
                      (popularModel: state.popularModel, recentlyModel: state.recentlyModel, favModel: state.favModel),
                  builder: (context, gameState) {
                    return GameHomePopularSection(
                      popularModel: gameState.popularModel,
                      recentlyModel: gameState.recentlyModel,
                      favModel: gameState.favModel,
                      onGameTap: (game) {
                        AuthUtil.checkIfLogin(() {
                          GameUtil().onClickGameCell(game: game);
                        });
                      },
                      onVenueTap: (platform) {
                        AuthUtil.checkIfLogin(() {
                          // TODO
                          // GameUtil().onClickPlatformCellBy(
                          //   platformCode: platform.code,
                          //   thirdPlatformId: platform.data.first.thirdPlatformId,
                          // );
                        });
                      },
                      onGameFavTap: (game) {
                        AuthUtil.checkIfLogin(() async {
                          bool flag = await GameUtil().onClickGameFav(isFav: !game.isSavour, game: game);
                          if (mounted && flag) {
                            sl<GameHomeCubit>().fetchPopularAndFavList();
                          }
                        });
                      },
                    );
                  },
                );
              }

              return SizedBox(
                height: gameType.sectionHeight,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    /// 标题
                    GameHomeSectionTitle(title: gameType.name),

                    /// 奖池
                    if (GameHomeUtil.needShowJackPot(gameType.code)) ...[
                      BlocSelector<GameHomeCubit, GameHomeState, BonusPoolEntity?>(
                        selector: (state) => state.bonusPool,
                        builder: (context, bonusPool) {
                          return GameHomeJackpotWidget(
                            gameType: gameType.code,
                            value: bonusPool,
                          );
                        },
                      ),
                    ],
                    GridView.builder(
                      padding: EdgeInsets.zero,
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      // 禁用内部滚动
                      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: GameHomeUtil.gameGridViewCrossAxisCount, // 每行两个网格
                        childAspectRatio: GameHomeUtil.gameGridViewAspectRatio, // 子项宽高比
                        crossAxisSpacing: GameHomeUtil.gameGridViewCrossAxisSpacing, // 网格间水平间距
                        mainAxisSpacing: GameHomeUtil.gameGridViewMainAxisSpacing, // 网格间垂直间距
                      ),
                      itemCount: gameType.data.length,
                      itemBuilder: (context, index) {
                        final item = gameType.data[index];
                        return kChannel == 'JS'
                            ? PlatformListCellHorizon(
                                model: PlatformListCellViewModel.fromAny(gameType: gameType, platform: item),
                                onTap: () {
                                  context.read<GameHomeCubit>().onClickPlatformCell(gameType: gameType, platform: item);
                                },
                              )
                            : PlatformListCell(
                                model: PlatformListCellViewModel.fromAny(gameType: gameType, platform: item),
                                onTap: () {
                                  context.read<GameHomeCubit>().onClickPlatformCell(gameType: gameType, platform: item);
                                },
                              );
                      },
                    ),
                  ],
                ),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildContent() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Left side tab list 左侧分类列表
        // SizedBox(
        Container(
          margin: EdgeInsets.only(top: 4.gw),
          width: GameHomeUtil.leftTabListViewWidth,
          child: BlocSelector<GameHomeCubit, GameHomeState, List>(
            selector: (state) => state.gameTypeList,
            builder: (context, gameTypeList) {
              return ListView.separated(
                shrinkWrap: true,
                padding: EdgeInsets.only(left: paddingH, right: 10.gw, top: 5.gw, bottom: 10.gw),
                itemBuilder: (context, index) {
                  final gameType = gameTypeList[index];
                  final isSelected = context.read<GameHomeCubit>().state.currentTabIndex == index;
                  Widget cell = PlatformTabCell(
                    model: PlatformTabCellViewModel.fromDynamic(gameType),
                    isSel: isSelected,
                  );

                  if (_animatingIndex == index) {
                    cell = ScaleTransition(
                      scale: Tween<double>(begin: 1.0, end: 1.1).animate(
                        CurvedAnimation(
                          parent: _scaleController,
                          curve: Curves.easeInOut,
                        ),
                      ),
                      child: cell,
                    );
                  }

                  return InkWell(
                    onTap: () => _onTabCellTap(index),
                    child: cell,
                  );
                },
                separatorBuilder: (context, index) => SizedBox(height: 12.gw),
                itemCount: gameTypeList.length,
              );
            },
          ),
        ),
        // Right side content area 右侧游戏列表
        Expanded(child: _buildGridView()),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final statusBarHeight = MediaQuery.of(context).padding.top;
    _scrollController ??= _viewCoordinator.pageScrollController(0);
    _viewCoordinator.pinnedHeaderSliverHeightBuilder = () {
      // return 50;
      return statusBarHeight + kToolbarHeight;
    };
    _subScrollController ??= _viewCoordinator.newChildScrollController();
    _setupScrollListener();

    final cubit = context.read<GameHomeCubit>();
    return BlocBuilder<GameHomeCubit, GameHomeState>(
      builder: (context, gameStat1e) {
        return Scaffold(
          key: _scaffoldKey,
          drawer: BlocProvider(
            create: (BuildContext context) => HomeDrawerCubit(),
            child: const HomeDrawer(),
          ),
          appBar: EmptyAppBar(color: Theme.of(context).appBarTheme.backgroundColor),
          backgroundColor: context.theme.scaffoldBackgroundColor,
          body: Stack(
            children: [
              Listener(
                onPointerUp: _viewCoordinator.onPointerUp,
                child: CustomScrollView(
                  controller: _scrollController,
                  physics: const ClampingScrollPhysics(),
                  slivers: <Widget>[
                    SliverAppBar(
                      pinned: true,
                      toolbarHeight: 44.gw,
                      leading: const SizedBox.shrink(),
                      flexibleSpace: GameHomeTopBar(scaffoldKey: _scaffoldKey),
                    ),
                    SliverToBoxAdapter(child: SizedBox(height: 17.gw)),
                    // 轮播图
                    BlocSelector<GameHomeCubit, GameHomeState, List<HomeBannerEntity>>(
                      selector: (state) => state.bannerList,
                      builder: (context, bannerList) {
                        return SliverToBoxAdapter(
                            child: GameHomeBannerWidget(
                          banners: bannerList,
                          height: 266.gw,
                          paddingH: paddingH,
                          margin: EdgeInsets.symmetric(horizontal: paddingH),
                          onTap: cubit.userDidClickBanner,
                        ));
                      },
                    ),

                    SliverPadding(padding: EdgeInsets.only(bottom: 6.gw)),
                    // 滚动文字条
                    SliverToBoxAdapter(
                        child: BlocSelector<GameHomeCubit, GameHomeState, List<GameNoticeEntity>>(
                      selector: (state) => state.noticeList,
                      builder: (context, noticeList) {
                        return GameHomeMarqueeTextWidget(
                          height: 34.gw,
                          margin: EdgeInsets.symmetric(horizontal: paddingH),
                          data: noticeList,
                        );
                      },
                    )),
                    SliverPadding(padding: EdgeInsets.only(bottom: 12.gw)),

                    // 用户信息
                    SliverToBoxAdapter(
                      child: BlocBuilder<GameHomeCubit, GameHomeState>(
                        builder: (context, state) => GameHomeUserInfoWidget(
                          height: 70.gw,
                          showLoginTips: true,
                          margin: EdgeInsets.symmetric(horizontal: paddingH),
                          textColor: const Color(0xff666666),
                          onTapRefreshBalance: () async {
                            _balanceRefreshController.repeat();
                            await GameUtil.transferOutAllPlatform();
                            _balanceRefreshController.reset();
                          },
                        ),
                      ),
                    ),

                    SliverPadding(padding: EdgeInsets.only(bottom: 38.gw)),
                    // 悬停联动菜单+内容区域
                    SliverFillRemaining(
                      child: BlocBuilder<GameHomeCubit, GameHomeState>(
                        builder: (context, homeState) {
                          if (homeState.netState == NetState.loadingState && homeState.gameTypeList.isEmpty) {
                            return Container(
                              alignment: Alignment.center,
                              child: SizedBox(
                                width: 20.gw,
                                height: 20.gw,
                                child: CircularProgressIndicator(strokeWidth: 2.gw),
                              ),
                            );
                          }

                          if ((ConnectivityUtil().status.hasConnection == false && homeState.gameTypeList.isEmpty) ||
                              homeState.netState == NetState.errorShowRefresh) {
                            return SizedBox(
                              height: 500.gw,
                              child: NetErrorWidget(
                                title: "加载游戏列表失败",
                                refreshMethod: () => context.read<GameHomeCubit>().fetchData(),
                              ),
                            );
                          }
                          return _buildContent();
                        },
                      ),
                    ),
                  ],
                ),
              ),
              Positioned(
                bottom: 30,
                right: 10,
                child: DelayButton(
                  onTap: () => _handleRedPacketTap(),
                  width: 48.gw,
                  height: 48.gw,
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.8),
                    shape: BoxShape.circle,
                    border: Border.all(
                      width: 0.5,
                      color: Theme.of(context).dividerColor,
                    ),
                  ),
                  mainWidget: Image.asset(
                    Assets.iconChatRedPacketGif,
                    width: 45.gw,
                    height: 45.gw,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
