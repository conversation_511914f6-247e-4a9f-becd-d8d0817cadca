import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/config/bottom_nav_config.dart';
import 'package:wd/core/constants/constants.dart';
import 'package:wd/core/models/apis/home.dart';
import 'package:wd/core/models/apis/lobby_api.dart';
import 'package:wd/core/models/entities/game_entity.dart';
import 'package:wd/core/models/entities/game_v2_entity.dart';
import 'package:wd/core/models/entities/jump_model.dart';
import 'package:wd/core/models/view_models/popular_section_view_model.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/utils/auth_util.dart';
import 'package:wd/core/utils/game_recently_util.dart';
import 'package:wd/core/utils/game_util.dart';
import 'package:wd/core/utils/log_util.dart';
import 'package:wd/core/utils/polling_services/polliing_services.dart';
import 'package:wd/core/utils/system_util.dart';
import 'package:wd/features/page/0_tiktok/video_home_cubit.dart';
import 'package:wd/features/page/main/screens/main_screen_cubit.dart';
import 'package:wd/features/routers/app_router.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/features/routers/route_tracker.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/easy_loading.dart';
import 'package:flutter/material.dart';
import 'package:wd/shared/widgets/game/popular/game_home_popular_list_view.dart';

import 'game_home_state.dart';

class GameHomeCubit extends Cubit<GameHomeState> {
  PageController? popularPageController;

  GameHomeCubit() : super(const GameHomeState());



  /// 开启奖金池轮询，仅在游戏首页生效
  startBonusPoolPolling() {
    sl<PollingService>().startPolling(
        id: kGSGameHomeBonusPool,
        keepAlive: true,
        onPoll: () async {
          final res = await LobbyApi.getBonusPool();
          emit(state.copyWith(bonusPool: res));
          return true;
        },
        interval: const Duration(seconds: 5),
        shouldPause: () =>
            RouteTracker().getCurrentRouteName() != AppRouter.nav ||
            sl<MainScreenCubit>().state.currentTabType != BottomNavType.gameHome);
  }

  void initPopularControllers(TickerProvider vsync) {
    if  (popularPageController != null) {
      // 先将控制器从 UI 中移除
      popularPageController?.dispose();
      popularPageController = null;
      emit(state.copyWith(currentPopularIndex: 0));
    }
    
    popularPageController = PageController(
      initialPage: state.currentPopularIndex,
    );
  }

  void onTabPageChanging(int index) {
      popularPageController?.jumpToPage(index);
      emit(state.copyWith(
        currentPopularIndex: index,
        needUpdatePopularHeight: true,
      ));

  }

  void onPopularPageChanged(int index) {
    emit(state.copyWith(currentPopularIndex: index));
  }

  @override
  Future<void> close() {
    popularPageController?.dispose();
    return super.close();
  }

  fetchRecentlyPlayList() async {
    GameRecentlyUtil().addListener(() {
      _updateRecentlyModel();
    });
    _updateRecentlyModel();
  }

  void _updateRecentlyModel() {
    final recentlyModel = PopularSectionViewModel(
      gameList: GameRecentlyUtil().gameList,
      venueList: GameRecentlyUtil().platformList,
    );
    emit(state.copyWith(recentlyModel: recentlyModel));
  }

  fetchPopularAndFavList() async {
    // TODO fetchPopularAndFavList
    final res = await GameUtil().fetchFavList();
    if (res != null) {
      GameRecentlyUtil().onChangeGameFav();

      // TODO 创建新的 PopularSectionViewModel 实例
      /// 热门
      // final newPopularModel = PopularSectionViewModel(
      //   gameList: res.popularGame.map((e) => Game.fromPopularGame(e)).toList(),
      //   venueList: res.popularVenue.map((e) => GamePlatform.fromPopularVenue(e)).toList(),
      // );

      /// 收藏
      // final newFavModel = PopularSectionViewModel(
      //   gameList: res.userSavourGame.map((e) => Game.fromPopularGame(e)).toList(),
      // );
      //
      // // 使用 copyWith 创建新的状态
      // final newState = state.clone(
      //   popularModel: newPopularModel,
      //   favModel: newFavModel,
      //   // 添加一个时间戳或其他变化值，确保状态更新
      //   needUpdatePopularHeight: !state.needUpdatePopularHeight,
      // );
      //
      // emit(newState);
    }
  }

  fetchGameList() async {
    try {
      // 请求数据
      List<GameTypeV2> list = await GameUtil().fetchGameList<GameTypeV2>();

      /// 设置热门平台数据
      emit(state.copyWith(
        gameTypeList: list,
        sectionHeights: list.map((e) {
          if (e.name == "热门") return 0.0;
          return e.sectionHeight;
        }).toList(),
      ));
    } catch (e) {
      emit(state.copyWith(netState: NetState.errorShowRefresh));
    } finally {
      emit(
          state.copyWith(netState: state.gameTypeList.isEmpty ? NetState.errorShowRefresh : NetState.dataSuccessState));
      if (state.gameTypeList.isEmpty) {
        // 如果缓存数据存在，直接更新状态
        if (GameUtil().gameList.isNotEmpty) {
          emit(state.copyWith(
            netState: NetState.dataSuccessState,
            gameTypeList: GameUtil().gameList,
          ));
        }
      }
    }
  }

  /// 点击红包
  Future<bool> checkRedPacketEligibility() async {
    GSEasyLoading.showLoading();
    final isEligible = await LobbyApi.checkLobbyRedPacketEligibility();
    GSEasyLoading.dismiss();

    if (!isEligible) {
      GSEasyLoading.showToast('暂无可领取的红包');
    }
    return isEligible;
  }

  void fetchData() {
    fetchPopularAndFavList();
    fetchGameList();
    if (state.bannerList.isEmpty) {
      fetchBannerList();
    }
    if (state.noticeList.isEmpty) {
      fetchNoticeList();
    }
  }

  /// 获取轮播图
  void fetchBannerList() async {
    final list = await HomeApi.fetchHomeBannerList();
    emit(state.copyWith(bannerList: list));
  }

  /// 用户点击banner
  void userDidClickBanner(int index) {
    final model = state.bannerList[index];
    final jumpModel = JumpModel.fromHomeBannerEntity(model);

    LogD("jumpModel>>>> ${jumpModel.toJson()}");
    SystemUtil.onJump(jumpModel);
  }

  /// 点击平台（品牌）
  onClickPlatformCell({required dynamic gameType, required dynamic platform}) {
    onClickPlatformCellV2(gameType: gameType, platform: platform);
  }

  onClickPlatformCellV2({required GameTypeV2 gameType, required GamePlatformV2 platform}) {
    if (platform.isGame == 1) {
      /// isGame == 1， 是游戏，直接登录
      AuthUtil.checkIfLogin(() {
        GameUtil().fetchGameLoginData(
          gameId: 0,
          platformId: platform.id,
        );
      });
    } else {
      sl<NavigatorService>().push(AppRouter.gameListV2, arguments: {"gameType": gameType, "gamePlatform": platform});
    }
  }

  void fetchNoticeList() async {
    final list = await LobbyApi.getGameNoticeList();
    emit(state.copyWith(noticeList: list));
  }

  onChangeCurrentTabIndex(int index) {
    if (state.currentTabIndex != index) {
      emit(state.copyWith(currentTabIndex: index));
    }
  }
}
