import 'dart:convert';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:wd/core/base/base_state.dart';

import 'package:wd/core/constants/enums.dart';
import 'package:wd/core/utils/auth_manager/auth_manager.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/system_util.dart';
import 'package:wd/shared/mixin/hide_float_button_route_aware_mixin.dart';
import 'package:wd/shared/widgets/animation/scale_animation.dart';
import 'package:wd/features/routers/app_router.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/common_button.dart';
import 'package:wd/shared/widgets/common_checkbox.dart';
import 'package:wd/shared/widgets/common_tabbar.dart';
import 'package:wd/shared/widgets/common_textfield.dart';
import 'package:wd/shared/widgets/gstext_image_button.dart';
import 'package:wd/shared/widgets/login/login_card_container.dart';
import 'package:wd/shared/widgets/icon_textfield.dart';

import 'auth/auth_cubit.dart';
import 'login_cubit.dart';
import 'login_state.dart';
import 'register/register_view.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/theme/custom_text_theme.dart';

class LoginPage extends StatefulWidget {
  final String? inviteCode;
  final String? channelCode;

  const LoginPage({super.key, this.inviteCode, this.channelCode});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> with HideFloatButtonRouteAwareMixin {
  /// Stores the captcha image as bytes
  Uint8List? imageBytes;

  /// Spacing constants for consistent layout
  static const double _verticalSpacing = 15.0;
  static const double _sectionSpacing = 30.0;

  @override
  Widget build(BuildContext context) {
    return BlocListener<LoginCubit, LoginState>(
      listenWhen: (previous, current) => previous.captchaModel?.img != current.captchaModel?.img,
      listener: (context, state) {
        final img = state.captchaModel?.img;
        if (img != null && img.isNotEmpty) {
          final base64String = img.split(',').last;
          setState(() => imageBytes = base64Decode(base64String));
        }
      },
      child: BlocBuilder<AuthCubit, AuthState>(
        builder: (context, state) {
          return LoginCardContainer(
            title: state.authType == AuthType.login
                ? Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Text(
                        "Login with",
                        style: context.textTheme.regular.w700.ffAne.copyWith(fontSize: 46.sp, height: 1.0),
                      ),
                      Transform.translate(
                        offset: Offset(0, -8.gw),
                        child: Text(
                          "Username",
                          style: context.textTheme.primary.w700.ffAne.copyWith(fontSize: 46.sp, height: 1.0),
                        ),
                      ),
                    ],
                  )
                : Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Text(
                        "Register with",
                        style: context.textTheme.regular.w700.ffAne.copyWith(fontSize: 46.sp, height: 1.0),
                      ),
                      Transform.translate(
                        offset: Offset(0, -8.gw),
                        child: Text(
                          "Username",
                          style: context.textTheme.primary.w700.ffAne.copyWith(fontSize: 46.sp, height: 1.0),
                        ),
                      ),
                    ],
                  ),
            subtitle: state.authType == AuthType.login
                ? Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        "Have account? ",
                        style: context.textTheme.title.fs16.ffAne,
                      ),
                      Text(
                        "Sign in",
                        style: context.textTheme.title.fs16.ffAne.copyWith(color: context.colorTheme.borderE),
                      ),
                    ],
                  )
                : Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        "Already have account? ",
                        style: context.textTheme.title.fs16.ffAne,
                      ),
                      Text(
                        "Sign in",
                        style: context.textTheme.secondary.fs16.ffAne.copyWith(color: const Color(0xFFFFD600)),
                      ),
                    ],
                  ),
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 40.gw),
              child: Column(
                children: [
                  _buildLoginTabs(),
                  SizedBox(height: _sectionSpacing.gw),
                  if (state.authType == AuthType.login)
                    _buildLogin()
                  else
                    RegisterPage(inviteCode: widget.inviteCode, channelCode: widget.channelCode),
                  10.verticalSpace,
                  _buildAuthBtn(),
                  10.verticalSpace,
                  _buildPhoneLoginDivider(),
                  SizedBox(height: 40.gw),
                  _buildBottomButtons(),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  _buildAuthBtn() {
    return InkWell(
      onTap: () async {
        final idToken = await AuthManager.signIn(LoginProvider.google);
        if (idToken != null && mounted) {
          context.read<LoginCubit>().performThirdAuthLoginApi(LoginType.google, token: idToken);
        }
      },
      child: Container(
        width: 94.gw,
        height: 46.gw,
        decoration: const BoxDecoration(color: Color(0xff212121), borderRadius: BorderRadius.all(Radius.circular(8))),
        alignment: Alignment.center,
        child: SvgPicture.asset("assets/images/login/login_google.svg", width: 20.gw, height: 20.gw),
      ),
    );
  }

  /// 构建登录/注册切换标签
  /// Build login/register switch tabs
  Widget _buildLoginTabs() {
    final authType = context.watch<AuthCubit>().state.authType;
    final currentIndex = authType == AuthType.login ? 0 : 1;

    return CommonTabBar(
      [
        CommonTabBarItem(title: 'Login', imageUrl: 'assets/images/login/login_tab_icon.png'),
        CommonTabBarItem(title: 'Sign Up', imageUrl: 'assets/images/login/register_tab_icon.png'),
      ],
      style: CommonTabBarStyle.secondary,
      currentIndex: currentIndex,
      shrinkWrap: true, // ✅ enable content-width layout
      tabPadding: EdgeInsets.symmetric(horizontal: 14.gw),
      onTap: (index) {
        final newAuthType = index == 0 ? AuthType.login : AuthType.register;
        context.read<AuthCubit>().switchAuthType(newAuthType);
      },
    );
  }

  /// 根据登录类型构建对应的登录表单
  /// Build corresponding login form based on login type
  Widget _buildLogin() {
    return BlocBuilder<LoginCubit, LoginState>(
      builder: (context, state) {
        // 如果当前登录类型不被支持，自动切换到支持的类型
        if (state.loginType == LoginType.phone && !state.authMethodType.supportsPhone) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            context.read<LoginCubit>().switchLoginType();
          });
          return const SizedBox();
        }
        if (state.loginType == LoginType.userName && !state.authMethodType.supportsAccount) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            context.read<LoginCubit>().switchLoginType();
          });
          return const SizedBox();
        }

        return state.loginType == LoginType.userName ? _buildPasswordLoginForm() : _buildPhoneLoginForm();
      },
    );
  }

  /// 构建切换登录方式的按钮
  /// @param title 按钮文本
  /// @param iconPath 图标路径
  /// @param targetType 目标登录类型
  ///
  /// Build login type switch button
  /// @param title Button text
  /// @param iconPath Icon path
  /// @param targetType Target login type
  Widget _buildSwitchLoginTypeButton({
    required String title,
    required String iconPath,
    required LoginType targetType,
  }) {
    return CommonButton(
      title: title,
      textColor: const Color(0xffB9936D),
      backgroundColor: Colors.transparent,
      prefix: Image.asset(iconPath, width: 28.gw, height: 28.gw),
      onPressed: () {
        // 切换登录类型（用户名密码/手机验证码）
        // Switch login type (username-password/phone-verification)
        // context.read<AuthCubit>().switchLoginType(targetType);
        // 重置当前登录表单状态
        // Reset current login form state
        context.read<LoginCubit>().switchLoginType();
      },
    );
  }

  /// 构建登录按钮，包含加载状态
  /// Build login button with loading state
  Widget _buildLoginButton(LoginState state) {
    return CommonButton(
      title: "sign_in".tr(),
      textColor: context.colorTheme.btnTitlePrimary,
      showLoading: state.loginStatus == SimplyNetStatus.loading,
      onPressed: () {
        // 获取当前登录类型
        // Get current login type
        final loginType = state.loginType;
        // 根据当前登录类型执行登录操作
        // Execute login operation based on current login type
        context.read<LoginCubit>().login(loginType);
      },
    );
  }

  /// 构建用户名密码登录表单
  /// Build username-password login form
  Widget _buildPasswordLoginForm() {
    return BlocBuilder<LoginCubit, LoginState>(
      builder: (context, state) {
        return CommonScaleAnimationWidget(
          children: [
            _buildUsernameInput(state),
            SizedBox(height: _verticalSpacing.gw),
            _buildPasswordInput(state),
            SizedBox(height: _verticalSpacing.gw),
            if (state.captchaType == CaptchaType.picture) ...[
              _buildCaptchaInput(state),
              SizedBox(height: _verticalSpacing.gw),
            ],
            _buildPrivacyPolicySwitch(state),
            SizedBox(height: _sectionSpacing.gw),
            _buildLoginButton(state),
            SizedBox(height: _verticalSpacing.gw),
            _buildPasswordOptions(state),

            // 只有当配置支持手机号登录时才显示切换按钮
            if (state.authMethodType.supportsPhone) ...[
              SizedBox(height: 12.gw),
              _buildSwitchLoginTypeButton(
                title: "phone_login".tr(), // 手机号登录
                iconPath: "assets/images/login/check.png",
                targetType: LoginType.phone,
              ),
            ],
          ],
        );
      },
    );
  }

  /// Builds the username input field
  Widget _buildUsernameInput(LoginState state) {
    return IconTextfield(
      textController: state.usernameController,
      hintText: "enter_username_or_phone".tr(), // 请输入用户名/手机号,
      icon: Icons.person_outline,
      onIconPressed: () {},
    );
  }

  /// Builds the password input field with visibility toggle
  Widget _buildPasswordInput(LoginState state) {
    return IconTextfield(
      textController: state.passwordController,
      hintText: "password_rule".tr(),
      icon: Icons.lock_outline,
      onIconPressed: () => context.read<LoginCubit>().togglePasswordVisibility(),
    );
  }

  /// Builds the privacy policy acceptance switch
  Widget _buildPrivacyPolicySwitch(LoginState state) {
    return Row(
      children: [
        CommonCheckbox(
          text: 'I Accept the Privacy Policy',
          value: state.rememberMe, // You may want to add a separate field for privacy policy acceptance
          onChanged: (value) => context.read<LoginCubit>().toggleRememberMe(value),
        ),
      ],
    );
  }

  /// Builds the "Remember me" checkbox and "Forgot password" link
  Widget _buildPasswordOptions(LoginState state) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        CommonCheckbox(
          text: 'remember_password'.tr(),
          value: state.rememberMe,
          onChanged: (value) => context.read<LoginCubit>().toggleRememberMe(value),
        ),
        GestureDetector(
          // onTap: () => SystemUtil.contactService(),
          onTap: () => sl<NavigatorService>().push(AppRouter.forgot),
          child: Text(
            'forgot_password'.tr(),
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(fontSize: 14.fs, color: const Color(0xffB9936D)),
          ),
        ),
      ],
    );
  }

  Widget _buildCaptchaInput(LoginState state) {
    return BlocBuilder<LoginCubit, LoginState>(
      builder: (context, state) {
        return Padding(
          padding: EdgeInsets.only(top: 10.gw),
          child: CommonTextField(
            controller: state.captchaController,
            hintText: "verification_code".tr(),
            keyboardType: TextInputType.number,
            isUnderline: true,
            suffixIcon: GestureDetector(
              onTap: () => context.read<LoginCubit>().fetchImageCaptcha(),
              behavior: HitTestBehavior.opaque,
              child: Container(
                alignment: Alignment.center,
                width: 100.gw,
                height: 45.gw,
                child: imageBytes != null
                    ? Image.memory(imageBytes!) // 只更新图片，不重新渲染 TextField
                    : Text('get_code'.tr(), style: Theme.of(context).textTheme.titleLarge),
              ),
            ),
            onChanged: (value) => context.read<LoginCubit>().setVerificationCode(value),
          ),
        );
      },
    );
  }

  /// 构建手机验证码登录表单
  /// Build phone verification login form
  Widget _buildPhoneLoginForm() {
    return BlocBuilder<LoginCubit, LoginState>(
      builder: (context, state) {
        return CommonScaleAnimationWidget(
          children: [
            _buildPhoneInput(state),
            SizedBox(height: _verticalSpacing.gw),
            _buildVerificationCodeInput(),
            SizedBox(height: _verticalSpacing.gw),
            if (state.captchaType == CaptchaType.picture) ...[
              _buildCaptchaInput(state),
              SizedBox(height: _verticalSpacing.gw),
            ],
            _buildPrivacyPolicySwitch(state),
            SizedBox(height: _verticalSpacing.gw),
            _buildLoginButton(state),

            // 只有当配置支持账号登录时才显示切换按钮
            if (state.authMethodType.supportsAccount) ...[
              SizedBox(height: 12.gw),
              _buildSwitchLoginTypeButton(
                title: "member_login".tr(),
                iconPath: "assets/images/login/lock.png",
                targetType: LoginType.userName,
              ),
            ],
          ],
        );
      },
    );
  }

  /// Builds the phone input field with country code
  Widget _buildPhoneInput(LoginState state) {
    return IconTextfield(
      textController: state.phoneController,
      hintText: "hint_enter_phone".tr(),
      icon: Icons.phone_outlined,
      onIconPressed: () {},
    );
  }

  /// Builds the verification code input field with request button
  Widget _buildVerificationCodeInput() {
    return BlocBuilder<LoginCubit, LoginState>(
      builder: (context, state) {
        return IconTextfield(
          textController: state.verificationCodeController,
          hintText: "hint_enter_verification_code".tr(),
          icon: Icons.shield_outlined,
          onIconPressed: () {},
        );
      },
    );
  }

  /// Builds the phone login divider with text button
  Widget _buildPhoneLoginDivider() {
    return Row(
      children: [
        Expanded(
          child: Container(
            height: 1,
            color: Colors.white.withOpacity(0.3),
          ),
        ),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.gw),
          child: GestureDetector(
            onTap: () {
              // Switch to phone login
              context.read<LoginCubit>().switchLoginType();
            },
            child: Text(
              'Login with Phone Number',
              style: context.textTheme.secondary.copyWith(
                color: Colors.white.withOpacity(0.8),
                decoration: TextDecoration.underline,
                decorationColor: Colors.white.withOpacity(0.8),
              ),
            ),
          ),
        ),
        Expanded(
          child: Container(
            height: 1,
            color: Colors.white.withOpacity(0.3),
          ),
        ),
      ],
    );
  }

  /// Builds the bottom action buttons
  Widget _buildBottomButtons() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20.gw),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          GSTextImageButton(
            text: "just_play".tr(),
            imageAssets: "assets/images/common/icon_game.svg",
            onPressed: () => sl<NavigatorService>().popUntilOrPush(AppRouter.nav),
          ),
          GSTextImageButton(
            text: "contact_support".tr(),
            imageAssets: "assets/images/common/icon_contact.svg",
            onPressed: () => SystemUtil.contactService(),
          ),
        ],
      ),
    );
  }
}

/// 登录类型切换的关键流程：
/// Key processes for login type switching:
///
/// 1. 顶部标签切换（登录/注册）：
///    Top tab switching (login/register):
///    - 调用 AuthCubit.switchAuthType 切换认证类型
///    - Call AuthCubit.switchAuthType to switch authentication type
///    - 同时重置登录和注册表单状态
///    - Simultaneously reset login and register form states
///
/// 2. 登录方式切换（密码/手机验证码）：
///    Login method switching (password/phone verification):
///    - 调用 AuthCubit.switchLoginType 切换登录类型
///    - Call AuthCubit.switchLoginType to switch login type
///    - 调用 LoginCubit.switchLoginType 重置表单状态
///    - Call LoginCubit.switchLoginType to reset form state
///    - 根据不同登录类型显示对应的表单界面
///    - Display corresponding form interface based on login type
///
/// 3. 登录操作：
///    Login operation:
///    - 获取当前登录类型
///    - Get current login type
///    - 根据登录类型调用对应的登录方法
///    - Call corresponding login method based on login type
