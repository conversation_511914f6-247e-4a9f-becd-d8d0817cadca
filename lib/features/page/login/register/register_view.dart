import 'dart:convert';
import 'dart:typed_data';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/constants/constants.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/global_config.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/mixin/hide_float_button_route_aware_mixin.dart';
import 'package:wd/shared/widgets/animation/scale_animation.dart';
import 'package:wd/features/page/login/register/register_state.dart';
import 'package:wd/shared/widgets/auth/phone_prefix.dart';
import 'package:wd/shared/widgets/common_button.dart';
import 'package:wd/shared/widgets/common_textfield.dart';
import 'package:wd/shared/widgets/easy_loading.dart';

import '../../../../core/constants/enums.dart';
import '../../../../shared/widgets/verification_code/verification_code.dart';
import '../auth/auth_cubit.dart';
import 'register_cubit.dart';

class RegisterPage extends StatefulWidget {
  final String? inviteCode;
  final String? channelCode;

  const RegisterPage({
    super.key,
    this.inviteCode,
    this.channelCode,
  });

  @override
  State<StatefulWidget> createState() => _RegisterPageState();
}

class _RegisterPageState extends State<RegisterPage> with HideFloatButtonRouteAwareMixin {
  static const double _kVerticalSpacing = 15.0;
  static const double _kHorizontalSpacing = 10.0;
  static const double _kIconSize = 14.0;
  static const double _kPhoneDividerHeight = 20.0;

  String? _inviteCode;
  Uint8List? _imageBytes;

  @override
  void initState() {
    super.initState();
    context.read<RegisterCubit>().initConfig();
    WidgetsBinding.instance.addPostFrameCallback(_initializeData);
  }

  void _initializeData(_) {
    _initializeInviteCode();
    _initializeChannelCode();
  }

  Future<void> _initializeInviteCode() async {
    final code = widget.inviteCode ?? GlobalConfig().inviteCode ?? (kDebug ? "10000001" : "88888888");

    context.read<RegisterCubit>().setInviteCode(code);
    setState(() => _inviteCode = code);
  }

  Future<void> _initializeChannelCode() async {
    final code = widget.channelCode ?? GlobalConfig().channelCode;
    if (code != null) {
      context.read<RegisterCubit>().setChannelCodeCode(code);
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<RegisterCubit, RegisterState>(
      listenWhen: _registerStateListenWhen,
      listener: _handleRegisterStateChanges,
      child: BlocBuilder<AuthCubit, AuthState>(
        builder: _buildMainContent,
      ),
    );
  }

  bool _registerStateListenWhen(RegisterState previous, RegisterState current) {
    return previous.registerStatus != current.registerStatus || previous.captchaModel?.img != current.captchaModel?.img;
  }

  void _handleRegisterStateChanges(BuildContext context, RegisterState state) {
    final img = state.captchaModel?.img;
    if (img?.isNotEmpty ?? false) {
      final base64String = img!.split(',').last;
      setState(() => _imageBytes = base64Decode(base64String));
    }
  }

  Widget _buildMainContent(BuildContext context, AuthState authState) {
    return BlocBuilder<RegisterCubit, RegisterState>(
      builder: (context, state) {
        if (state.registerType == LoginType.phone && !state.authMethodType.supportsPhone) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            context.read<RegisterCubit>().switchRegisterType();
          });
        } else if (state.registerType == LoginType.userName && !state.authMethodType.supportsAccount) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            context.read<RegisterCubit>().switchRegisterType();
          });
        }

        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildRegistrationForm(state.registerType),
            SizedBox(height: 40.gw),
            _buildBottomSection(state),
          ],
        );
      },
    );
  }

  Widget _buildRegistrationForm(LoginType type) {
    return type == LoginType.userName ? _buildPasswordRegisterForm() : _buildPhoneRegisterForm();
  }

  Widget _buildPasswordRegisterForm() {
    return BlocBuilder<RegisterCubit, RegisterState>(
      builder: (context, state) {
        return CommonScaleAnimationWidget(
          children: [
            _buildLabel(title: 'username'.tr(), path: 'assets/images/login/icon_login_name.png'),
            _buildUsernameField(state),
            SizedBox(height: _kVerticalSpacing.gw),
            _buildLabel(title: 'password'.tr(), path: 'assets/images/login/icon_login_password.png'),
            _buildPasswordField(
              controller: TextEditingController(),
              hintText: "password_rule".tr(),
              onChanged: (value) => context.read<RegisterCubit>().setPassword(value),
            ),
            SizedBox(height: _kVerticalSpacing.gw),
            _buildLabel(title: 'confirm_password'.tr(), path: 'assets/images/login/icon_login_password.png'),
            _buildPasswordField(
              controller: TextEditingController(),
              hintText: "password_rule".tr(),
              onChanged: (value) => context.read<RegisterCubit>().setConfirmPassword(value),
            ),
          ],
        );
      },
    );
  }

  Widget _buildPhoneRegisterForm() {
    return BlocBuilder<RegisterCubit, RegisterState>(
      builder: (context, state) {
        return CommonScaleAnimationWidget(
          children: [
            _buildLabel(title: 'phone'.tr(), path: 'assets/images/login/phone.png'),
            _buildPhoneInput(state),
            SizedBox(height: _kVerticalSpacing.gw),
            _buildLabel(title: 'verification_code'.tr(), path: 'assets/images/login/shield.png'),
            _buildVerificationCodeInput(),
            SizedBox(height: _kVerticalSpacing.gw),
            _buildLabel(title: 'password'.tr(), path: 'assets/images/login/icon_login_password.png'),
            _buildPasswordField(
              controller: state.passwordController,
              hintText: "password_rule".tr(),
              onChanged: (value) => context.read<RegisterCubit>().setPassword(value),
            ),
            SizedBox(height: _kVerticalSpacing.gw),
            // 确认密码
            _buildLabel(title: 'confirm_password'.tr(), path: 'assets/images/login/icon_login_password.png'),
            _buildPasswordField(
              controller: state.confirmPasswordController,
              hintText: "password_rule".tr(),
              onChanged: (value) => context.read<RegisterCubit>().setConfirmPassword(value),
            ),
          ],
        );
      },
    );
  }

  /// Builds the phone input field with country code
  Widget _buildPhoneInput(RegisterState state) {
    return CommonTextField(
      controller: state.phoneController,
      hintText: "hint_enter_phone".tr(),
      keyboardType: TextInputType.phone,
      isUnderline: true,
      suffixIconPadding: EdgeInsets.zero,
      prefixIconPadding: EdgeInsets.zero,
      prefixIcon: const PhonePrefix(),
      maxLength: 11,
      onChanged: (value) => context.read<RegisterCubit>().setPhone(value),
    );
  }

  /// Builds the verification code input field with request button
  Widget _buildVerificationCodeInput() {
    return BlocBuilder<RegisterCubit, RegisterState>(
      builder: (context, state) {
        return CommonTextField(
          controller: state.verificationCodeController,
          hintText: "hint_enter_verification_code".tr(),
          keyboardType: TextInputType.number,
          isUnderline: true,
          suffixIcon: VerificationCode(
            phone: state.phone,
            onSmsCode: (smsCode) {
              if (kDebug && smsCode.isNotEmpty) {
                final cubit = context.read<RegisterCubit>();
                cubit.setSmsCode(smsCode);
                cubit.updateSmsCodeController(smsCode);
              }
            },
          ),
          onChanged: (value) => context.read<RegisterCubit>().setSmsCode(value),
        );
      },
    );
  }

  Widget _buildLabel({required String path, required String title}) {
    return Row(
      children: [
        Image.asset(
          path,
          width: _kIconSize.gw,
          height: _kIconSize.gw,
          color: const Color(0xff3B4165),
        ),
        SizedBox(width: _kHorizontalSpacing.gw),
        Text(
          title,
          style: TextStyle(
            fontSize: 16.fs,
            color: const Color(0xff3B4165),
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildUsernameField(RegisterState state) {
    return CommonTextField(
      controller: state.usernameController,
      hintText: "hint_register_username".tr(),
      isUnderline: true,
      onChanged: (value) => context.read<RegisterCubit>().setUsername(value),
    );
  }

  Widget _buildPasswordField({
    required String hintText,
    required ValueChanged<String> onChanged,
    TextEditingController? controller,
  }) {
    return BlocBuilder<RegisterCubit, RegisterState>(
      buildWhen: (previous, current) => previous.isPasswordVisible != current.isPasswordVisible,
      builder: (context, state) {
        return CommonTextField(
          controller: controller,
          hintText: hintText,
          isUnderline: true,
          onChanged: onChanged,
          obscureText: !state.isPasswordVisible,
          suffixIcon: IconButton(
            icon: Icon(
              state.isPasswordVisible ? Icons.visibility : Icons.visibility_off,
              color: Colors.grey,
            ),
            onPressed: () => context.read<RegisterCubit>().togglePasswordVisibility(),
          ),
        );
      },
    );
  }

  Widget _buildInviteCodeField() {
    return CommonTextField(
      hintText: "invite_code".tr(),
      isUnderline: true,
      onChanged: (value) => context.read<RegisterCubit>().setInviteCode(value),
    );
  }

  Widget _buildVerificationCodeField(RegisterState state) {
    return CommonTextField(
      controller: state.verificationCodeController,
      hintText: "verification_code".tr(),
      isUnderline: true,
      keyboardType: TextInputType.number,
      suffixIcon: GestureDetector(
        onTap: () => context.read<RegisterCubit>().fetchImageCaptcha(),
        behavior: HitTestBehavior.opaque,
        child: Container(
          alignment: Alignment.center,
          width: 100.gw,
          height: 45.gw,
          child: _imageBytes != null
              ? Image.memory(_imageBytes!)
              : Text('get_code'.tr(), style: Theme.of(context).textTheme.titleLarge),
        ),
      ),
      onChanged: (value) => context.read<RegisterCubit>().setVerificationCode(value),
    );
  }

  Widget _buildBottomSection(RegisterState state) {
    final registerType = state.registerType;
    return Column(
      children: [
        _buildRegisterButton(),
        SizedBox(height: 12.gw),
        if (registerType == LoginType.userName && state.authMethodType.supportsPhone)
          CommonButton(
            title: "phone_register".tr(),
            textColor: const Color(0xffB9936D),
            prefix: Image.asset('assets/images/login/check.png', width: 28.gw, height: 28.gw),
            backgroundColor: Colors.transparent,
            onPressed: () {
              context.read<RegisterCubit>().switchRegisterType();
            },
          )
        else if (registerType == LoginType.phone && state.authMethodType.supportsAccount)
          CommonButton(
            title: "account_register".tr(), // 账号注册
            textColor: const Color(0xffB9936D),
            prefix: Image.asset('assets/images/login/lock.png', width: 28.gw, height: 28.gw),
            backgroundColor: Colors.transparent,
            onPressed: () {
              context.read<RegisterCubit>().switchRegisterType();
            },
          ),
        SizedBox(height: 20.gw),
        _buildLoginLink(),
      ],
    );
  }

  Widget _buildRegisterButton() {
    return BlocBuilder<RegisterCubit, RegisterState>(
      builder: (context, state) {
        return CommonButton(
          title: "sign_up".tr(),
          textColor: const Color(0xff7E6245),
          bgImgPath: "assets/images/common/bg_btn_golden.png",
          backgroundColor: Colors.transparent,
          showLoading: state.registerStatus == SimplyNetStatus.loading,
          onPressed: () {
            final registerType = state.registerType;

            if ((registerType == LoginType.phone && !state.authMethodType.supportsPhone) ||
                (registerType == LoginType.userName && !state.authMethodType.supportsAccount)) {
              GSEasyLoading.showToast('register_method_unavailable'.tr()); // 当前注册方式不可用
              return;
            }

            FocusScope.of(context).unfocus();
            context.read<RegisterCubit>().register(loginType: registerType);
          },
        );
      },
    );
  }

  Widget _buildLoginLink() {
    return BlocBuilder<RegisterCubit, RegisterState>(
      builder: (context, state) {
        return GestureDetector(
          onTap: () {

            context.read<AuthCubit>()
              .switchAuthType(AuthType.login);
            context.read<RegisterCubit>().switchRegisterType();
          },
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'existing_account'.tr(),
                style: Theme.of(context).textTheme.titleMedium,
              ),
              Text(
                'login_now'.tr(),
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Theme.of(context).primaryColor,
                  decoration: TextDecoration.underline,
                  decorationColor: Theme.of(context).primaryColor,
                  decorationThickness: 1.0,
                  height: 1.5,
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
