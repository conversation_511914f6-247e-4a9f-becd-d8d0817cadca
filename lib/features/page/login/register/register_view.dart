import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/constants/assets.dart';
import 'package:wd/core/constants/constants.dart';

import 'package:wd/core/utils/global_config.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/mixin/hide_float_button_route_aware_mixin.dart';

import 'package:wd/features/page/login/register/register_state.dart';
import 'package:wd/shared/widgets/common_button.dart';
import 'package:wd/shared/widgets/easy_loading.dart';
import 'package:wd/shared/widgets/icon_textfield.dart';
import 'package:wd/shared/widgets/animation/scale_animation.dart';
import 'package:wd/core/theme/themes.dart';

import '../../../../core/constants/enums.dart';
import '../auth/auth_cubit.dart';
import 'register_cubit.dart';

class RegisterPage extends StatefulWidget {
  final String? inviteCode;
  final String? channelCode;

  const RegisterPage({
    super.key,
    this.inviteCode,
    this.channelCode,
  });

  @override
  State<StatefulWidget> createState() => _RegisterPageState();
}

class _RegisterPageState extends State<RegisterPage> with HideFloatButtonRouteAwareMixin {
  static const double _kVerticalSpacing = 15.0;
  static const double _kHorizontalSpacing = 10.0;
  static const double _kIconSize = 14.0;

  @override
  void initState() {
    super.initState();
    context.read<RegisterCubit>().initConfig();
    WidgetsBinding.instance.addPostFrameCallback(_initializeData);
  }

  void _initializeData(_) {
    _initializeInviteCode();
    _initializeChannelCode();
  }

  Future<void> _initializeInviteCode() async {
    final code = widget.inviteCode ?? GlobalConfig().inviteCode ?? (kDebug ? "10000001" : "88888888");

    context.read<RegisterCubit>().setInviteCode(code);
  }

  Future<void> _initializeChannelCode() async {
    final code = widget.channelCode ?? GlobalConfig().channelCode;
    if (code != null) {
      context.read<RegisterCubit>().setChannelCodeCode(code);
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<RegisterCubit, RegisterState>(
      listenWhen: _registerStateListenWhen,
      listener: _handleRegisterStateChanges,
      child: BlocBuilder<AuthCubit, AuthState>(
        builder: _buildMainContent,
      ),
    );
  }

  bool _registerStateListenWhen(RegisterState previous, RegisterState current) {
    return previous.registerStatus != current.registerStatus || previous.captchaModel?.img != current.captchaModel?.img;
  }

  void _handleRegisterStateChanges(BuildContext context, RegisterState state) {
    // Handle captcha image if needed
  }

  Widget _buildMainContent(BuildContext context, AuthState authState) {
    return BlocBuilder<RegisterCubit, RegisterState>(
      builder: (context, state) {
        if (state.registerType == LoginType.phone && !state.authMethodType.supportsPhone) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            context.read<RegisterCubit>().switchRegisterType();
          });
        } else if (state.registerType == LoginType.userName && !state.authMethodType.supportsAccount) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            context.read<RegisterCubit>().switchRegisterType();
          });
        }

        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildRegistrationForm(state.registerType),
            SizedBox(height: 40.gw),
          ],
        );
      },
    );
  }

  Widget _buildRegistrationForm(LoginType type) {
    return type == LoginType.userName ? _buildPasswordRegisterForm() : _buildPhoneRegisterForm();
  }

  Widget _buildPasswordRegisterForm() {
    return BlocBuilder<RegisterCubit, RegisterState>(
      builder: (context, state) {
        return Column(
          children: [
            // Input fields with staggered animation
            CommonScaleAnimationWidget(
              children: [
                _buildUsernameInput(state),
                SizedBox(height: _kVerticalSpacing.gw),
                _buildPasswordInput(state),
                SizedBox(height: _kVerticalSpacing.gw),
                _buildConfirmPasswordInput(state),
                SizedBox(height: _kVerticalSpacing.gw),
                _buildPrivacyPolicySwitch(state),
                SizedBox(height: _kVerticalSpacing.gw),

                // Sign Up button with scale animation
                AnimationConfiguration.synchronized(
                  duration: const Duration(milliseconds: 300),
                  child: ScaleAnimation(
                    scale: 0.9,
                    child: _buildRegisterButton(),
                  ),
                ),
              ],
            ),
            SizedBox(height: 20.gw),

            // Signup method buttons
            _buildSignupMethodButtons(state),
            SizedBox(height: 15.gw),

            // // Social login buttons with animation
            // AnimationConfiguration.synchronized(
            //   duration: const Duration(milliseconds: 600),
            //   child: SlideAnimation(
            //     verticalOffset: 50.0,
            //     child: FadeInAnimation(
            //       child: _buildSocialLoginButtons(),
            //     ),
            //   ),
            // ),
          ],
        );
      },
    );
  }

  Widget _buildPhoneRegisterForm() {
    return BlocBuilder<RegisterCubit, RegisterState>(
      builder: (context, state) {
        return Column(
          children: [
            // Title section with animation
            AnimationConfiguration.synchronized(
              duration: const Duration(milliseconds: 800),
              child: SlideAnimation(
                verticalOffset: 100.0,
                child: FadeInAnimation(
                  child: _buildTitleSection(),
                ),
              ),
            ),
            SizedBox(height: 30.gw),

            // Input fields with staggered animation
            CommonScaleAnimationWidget(
              children: [
                _buildPhoneInputField(state),
                SizedBox(height: _kVerticalSpacing.gw),
                _buildVerificationCodeInputField(state),
                SizedBox(height: _kVerticalSpacing.gw),
                _buildPasswordInput(state),
                SizedBox(height: _kVerticalSpacing.gw),
                _buildConfirmPasswordInput(state),
                SizedBox(height: 30.gw),

                // Sign Up button with scale animation
                AnimationConfiguration.synchronized(
                  duration: const Duration(milliseconds: 300),
                  child: ScaleAnimation(
                    scale: 0.9,
                    child: _buildRegisterButton(),
                  ),
                ),
              ],
            ),
            SizedBox(height: 20.gw),

            // Signup method buttons
            _buildSignupMethodButtons(state),
          ],
        );
      },
    );
  }

  /// Builds the title section
  Widget _buildTitleSection() {
    return BlocBuilder<RegisterCubit, RegisterState>(
      builder: (context, state) {
        final registerType = state.registerType;
        final title = registerType == LoginType.userName ? 'Register with\nUsername' : 'Register with\nPhone Number';

        return Column(
          children: [
            Text(
              title,
              textAlign: TextAlign.center,
              style: context.textTheme.primary.copyWith(
                fontSize: 28.gw,
                fontWeight: FontWeight.bold,
                color: Colors.white,
                height: 1.2,
              ),
            ),
          ],
        );
      },
    );
  }

  /// Builds the username input field
  Widget _buildUsernameInput(RegisterState state) {
    return IconTextfield(
        textController: state.usernameController,
        hintText: "hint_register_username".tr(),
        icon: IconButton(
          icon: Image.asset(Assets.iconLoginName, width: 20.gw, height: 20.gw),
          onPressed: () {},
        ),
        onChanged: (value) => context.read<RegisterCubit>().setUsername(value));
  }

  /// Builds the password input field with visibility toggle
  Widget _buildPasswordInput(RegisterState state) {
    return IconTextfield(
      textController: state.passwordController,
      hintText: "password_rule".tr(),
      icon: IconButton(
        icon: Image.asset(Assets.iconLoginPassword, width: 20.gw, height: 20.gw),
        // onPressed: () => context.read<RegisterCubit>().togglePasswordVisibility(),
        onPressed: () {},
      ),
      onChanged: (value) => context.read<RegisterCubit>().setPassword(value),
    );
  }

  /// Builds the confirm password input field
  Widget _buildConfirmPasswordInput(RegisterState state) {
    return IconTextfield(
      textController: state.confirmPasswordController,
      hintText: "password_rule".tr(),
      icon: IconButton(
        icon: Image.asset(Assets.iconLoginPassword, width: 20.gw, height: 20.gw),
        // onPressed: () => context.read<RegisterCubit>().togglePasswordVisibility(),
        onPressed: () {},
      ),
      onChanged: (value) => context.read<RegisterCubit>().setConfirmPassword(value),
    );
  }

  /// Builds the privacy policy acceptance switch
  Widget _buildPrivacyPolicySwitch(RegisterState state) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Transform.scale(
          scale: 0.6,
          alignment: Alignment.centerLeft,
          child: Switch(
            value: false, // TODO: Add acceptPrivacyPolicy to RegisterState
            onChanged: (value) {
              // TODO: Add togglePrivacyPolicy to RegisterCubit
            },
            activeColor: context.theme.primaryColor,
            inactiveThumbColor: context.colorTheme.textSecondary,
            inactiveTrackColor: context.colorTheme.textSecondary.withOpacity(0.3),
          ),
        ),
        Expanded(
          child: Text(
            'I Accept the Privacy Policy',
            style: context.textTheme.secondary.copyWith(
              color: context.colorTheme.textSecondary,
              fontSize: 14.gw,
            ),
          ),
        ),
      ],
    );
  }

  /// Builds the phone input field with country code
  Widget _buildPhoneInputField(RegisterState state) {
    return IconTextfield(
      textController: state.phoneController,
      hintText: "hint_enter_phone".tr(),
      icon: IconButton(
        icon: const Icon(Icons.phone_outlined),
        onPressed: () {},
      ),
      onChanged: (value) => context.read<RegisterCubit>().setPhone(value),
    );
  }

  /// Builds the verification code input field with request button
  Widget _buildVerificationCodeInputField(RegisterState state) {
    return IconTextfield(
      textController: state.verificationCodeController,
      hintText: "hint_enter_verification_code".tr(),
      icon: IconButton(
        icon: Image.asset(Assets.iconLoginShield, width: 20.gw, height: 20.gw),
        onPressed: () {},
      ),
      onChanged: (value) => context.read<RegisterCubit>().setVerificationCode(value),
    );
  }

  /// Builds the signup method buttons based on current signup type
  Widget _buildSignupMethodButtons(RegisterState state) {
    final currentType = state.registerType;

    // Determine which buttons to show based on current type and supported methods
    Widget? firstButton;
    Widget? secondButton;

    if (currentType == LoginType.userName) {
      // Currently username signup, show phone and email options
      if (state.authMethodType.supportsPhone) {
        firstButton = _buildSignupMethodButton(
          icon: Icons.phone_outlined,
          title: "Phone Sign Up",
          onTap: () {
            context.read<RegisterCubit>().switchRegisterType();
          },
        );
      }
      // Always show email option (placeholder for now)
      secondButton = _buildSignupMethodButton(
        icon: Icons.email_outlined,
        title: "Email Sign Up",
        onTap: () {
          // TODO: Implement email signup
        },
      );
    } else if (currentType == LoginType.phone) {
      // Currently phone signup, show username and email options
      if (state.authMethodType.supportsAccount) {
        firstButton = _buildSignupMethodButton(
          icon: Icons.person_outline,
          title: "Username Sign Up",
          onTap: () {
            context.read<RegisterCubit>().switchRegisterType();
          },
        );
      }
      // Always show email option (placeholder for now)
      secondButton = _buildSignupMethodButton(
        icon: Icons.email_outlined,
        title: "Email Sign Up",
        onTap: () {
          // TODO: Implement email signup
        },
      );
    }

    // If we don't have both buttons, don't show anything
    if (firstButton == null || secondButton == null) {
      return const SizedBox.shrink();
    }

    return AnimationConfiguration.synchronized(
      duration: const Duration(milliseconds: 400),
      child: FadeInAnimation(
        child: Row(
          children: [
            Expanded(child: firstButton),
            SizedBox(width: 15.gw),
            Expanded(child: secondButton),
          ],
        ),
      ),
    );
  }

  /// Builds a single signup method button
  Widget _buildSignupMethodButton({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 50.gw,
        decoration: BoxDecoration(
          color: context.colorTheme.textSecondary.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12.gw),
          border: Border.all(
            color: context.colorTheme.textSecondary.withOpacity(0.2),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: Colors.white,
              size: 20.gw,
            ),
            SizedBox(width: 8.gw),
            Text(
              title,
              style: context.textTheme.primary.copyWith(
                color: Colors.white,
                fontSize: 14.gw,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRegisterButton() {
    return BlocBuilder<RegisterCubit, RegisterState>(
      builder: (context, state) {
        const isEnabled = true; // TODO: Use acceptPrivacyPolicy when available

        return CommonButton(
          title: "sign_up".tr(),
          textColor: context.colorTheme.btnTitlePrimary,
          showLoading: state.registerStatus == SimplyNetStatus.loading,
          onPressed: isEnabled
              ? () {
                  final registerType = state.registerType;

                  if ((registerType == LoginType.phone && !state.authMethodType.supportsPhone) ||
                      (registerType == LoginType.userName && !state.authMethodType.supportsAccount)) {
                    GSEasyLoading.showToast('register_method_unavailable'.tr()); // 当前注册方式不可用
                    return;
                  }

                  FocusScope.of(context).unfocus();
                  context.read<RegisterCubit>().register(loginType: registerType);
                }
              : null, // Disable button when privacy policy not accepted
        );
      },
    );
  }

  /// Builds a small social button
  Widget _buildSocialButton({
    required String iconPath,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 80.gw,
        height: 40.gh,
        decoration: BoxDecoration(
          color: context.colorTheme.textSecondary.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12.gw),
          border: Border.all(
            color: context.colorTheme.textSecondary.withOpacity(0.2),
            width: 1,
          ),
        ),
        child: Image.asset(
          iconPath,
          color: Colors.white,
          width: 20.gw,
          height: 20.gh,
        ),
      ),
    );
  }
}
