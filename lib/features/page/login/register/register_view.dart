import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/constants/constants.dart';

import 'package:wd/core/utils/global_config.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/mixin/hide_float_button_route_aware_mixin.dart';

import 'package:wd/features/page/login/register/register_state.dart';
import 'package:wd/shared/widgets/auth/phone_prefix.dart';
import 'package:wd/shared/widgets/common_button.dart';
import 'package:wd/shared/widgets/common_textfield.dart';
import 'package:wd/shared/widgets/easy_loading.dart';
import 'package:wd/shared/widgets/icon_textfield.dart';
import 'package:wd/core/theme/themes.dart';

import '../../../../core/constants/enums.dart';
import '../../../../shared/widgets/verification_code/verification_code.dart';
import '../auth/auth_cubit.dart';
import 'register_cubit.dart';

class RegisterPage extends StatefulWidget {
  final String? inviteCode;
  final String? channelCode;

  const RegisterPage({
    super.key,
    this.inviteCode,
    this.channelCode,
  });

  @override
  State<StatefulWidget> createState() => _RegisterPageState();
}

class _RegisterPageState extends State<RegisterPage> with HideFloatButtonRouteAwareMixin {
  static const double _kVerticalSpacing = 15.0;
  static const double _kHorizontalSpacing = 10.0;
  static const double _kIconSize = 14.0;

  @override
  void initState() {
    super.initState();
    context.read<RegisterCubit>().initConfig();
    WidgetsBinding.instance.addPostFrameCallback(_initializeData);
  }

  void _initializeData(_) {
    _initializeInviteCode();
    _initializeChannelCode();
  }

  Future<void> _initializeInviteCode() async {
    final code = widget.inviteCode ?? GlobalConfig().inviteCode ?? (kDebug ? "10000001" : "88888888");

    context.read<RegisterCubit>().setInviteCode(code);
  }

  Future<void> _initializeChannelCode() async {
    final code = widget.channelCode ?? GlobalConfig().channelCode;
    if (code != null) {
      context.read<RegisterCubit>().setChannelCodeCode(code);
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<RegisterCubit, RegisterState>(
      listenWhen: _registerStateListenWhen,
      listener: _handleRegisterStateChanges,
      child: BlocBuilder<AuthCubit, AuthState>(
        builder: _buildMainContent,
      ),
    );
  }

  bool _registerStateListenWhen(RegisterState previous, RegisterState current) {
    return previous.registerStatus != current.registerStatus || previous.captchaModel?.img != current.captchaModel?.img;
  }

  void _handleRegisterStateChanges(BuildContext context, RegisterState state) {
    // Handle captcha image if needed
  }

  Widget _buildMainContent(BuildContext context, AuthState authState) {
    return BlocBuilder<RegisterCubit, RegisterState>(
      builder: (context, state) {
        if (state.registerType == LoginType.phone && !state.authMethodType.supportsPhone) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            context.read<RegisterCubit>().switchRegisterType();
          });
        } else if (state.registerType == LoginType.userName && !state.authMethodType.supportsAccount) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            context.read<RegisterCubit>().switchRegisterType();
          });
        }

        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildRegistrationForm(state.registerType),
            SizedBox(height: 40.gw),
            _buildBottomSection(state),
          ],
        );
      },
    );
  }

  Widget _buildRegistrationForm(LoginType type) {
    return type == LoginType.userName ? _buildPasswordRegisterForm() : _buildPhoneRegisterForm();
  }

  Widget _buildPasswordRegisterForm() {
    return BlocBuilder<RegisterCubit, RegisterState>(
      builder: (context, state) {
        return Column(
          children: [
            _buildUsernameInput(state),
            SizedBox(height: _kVerticalSpacing.gw),
            _buildPasswordInput(state),
            SizedBox(height: _kVerticalSpacing.gw),
            _buildConfirmPasswordInput(state),
            SizedBox(height: _kVerticalSpacing.gw),
            _buildPrivacyPolicySwitch(state),
            SizedBox(height: _kVerticalSpacing.gw),
            _buildRegisterButton(),

            // 只有当配置支持手机号注册时才显示切换按钮
            if (state.authMethodType.supportsPhone) ...[
              SizedBox(height: 20.gw),
              _buildPhoneRegisterDivider(),
            ],
          ],
        );
      },
    );
  }

  Widget _buildPhoneRegisterForm() {
    return BlocBuilder<RegisterCubit, RegisterState>(
      builder: (context, state) {
        return Column(
          children: [
            _buildPhoneInputField(state),
            SizedBox(height: _kVerticalSpacing.gw),
            _buildVerificationCodeInputField(state),
            SizedBox(height: _kVerticalSpacing.gw),
            _buildPasswordInput(state),
            SizedBox(height: _kVerticalSpacing.gw),
            _buildConfirmPasswordInput(state),
            SizedBox(height: _kVerticalSpacing.gw),
            _buildPrivacyPolicySwitch(state),
            SizedBox(height: _kVerticalSpacing.gw),
            _buildRegisterButton(),

            // 只有当配置支持账号注册时才显示切换按钮
            if (state.authMethodType.supportsAccount) ...[
              SizedBox(height: 20.gw),
              _buildUsernameRegisterDivider(),
            ],
          ],
        );
      },
    );
  }

  /// Builds the username input field
  Widget _buildUsernameInput(RegisterState state) {
    return IconTextfield(
      textController: state.usernameController,
      hintText: "hint_register_username".tr(),
      icon: Icons.person_outline,
      onIconPressed: () {},
    );
  }

  /// Builds the password input field with visibility toggle
  Widget _buildPasswordInput(RegisterState state) {
    return IconTextfield(
      textController: state.passwordController,
      hintText: "password_rule".tr(),
      icon: Icons.lock_outline,
      onIconPressed: () => context.read<RegisterCubit>().togglePasswordVisibility(),
    );
  }

  /// Builds the confirm password input field
  Widget _buildConfirmPasswordInput(RegisterState state) {
    return IconTextfield(
      textController: state.confirmPasswordController,
      hintText: "confirm_password_rule".tr(),
      icon: Icons.lock_outline,
      onIconPressed: () => context.read<RegisterCubit>().togglePasswordVisibility(),
    );
  }

  /// Builds the privacy policy acceptance switch
  Widget _buildPrivacyPolicySwitch(RegisterState state) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Transform.scale(
          scale: 0.6,
          alignment: Alignment.centerLeft,
          child: Switch(
            value: false, // TODO: Add acceptPrivacyPolicy to RegisterState
            onChanged: (value) {
              // TODO: Add togglePrivacyPolicy to RegisterCubit
            },
            activeColor: context.theme.primaryColor,
            inactiveThumbColor: context.colorTheme.textSecondary,
            inactiveTrackColor: context.colorTheme.textSecondary.withOpacity(0.3),
          ),
        ),
        Expanded(
          child: Text(
            'I Accept the Privacy Policy',
            style: context.textTheme.secondary.copyWith(
              color: context.colorTheme.textSecondary,
              fontSize: 14.gw,
            ),
          ),
        ),
      ],
    );
  }

  /// Builds the phone input field with country code
  Widget _buildPhoneInputField(RegisterState state) {
    return IconTextfield(
      textController: state.phoneController,
      hintText: "hint_enter_phone".tr(),
      icon: Icons.phone_outlined,
      onIconPressed: () {},
    );
  }

  /// Builds the verification code input field with request button
  Widget _buildVerificationCodeInputField(RegisterState state) {
    return IconTextfield(
      textController: state.verificationCodeController,
      hintText: "hint_enter_verification_code".tr(),
      icon: Icons.shield_outlined,
      onIconPressed: () {},
    );
  }

  /// Builds the phone register divider with text button
  Widget _buildPhoneRegisterDivider() {
    return Row(
      children: [
        Expanded(
          child: Container(
            height: 1,
            color: context.colorTheme.textSecondary.withOpacity(0.3),
          ),
        ),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.gw),
          child: GestureDetector(
            onTap: () {
              // Switch to phone register
              context.read<RegisterCubit>().switchRegisterType();
            },
            child: Text(
              'Register with Phone Number',
              style: context.textTheme.secondary.copyWith(
                color: context.colorTheme.textSecondary,
                decoration: TextDecoration.underline,
                decorationColor: context.colorTheme.textSecondary,
              ),
            ),
          ),
        ),
        Expanded(
          child: Container(
            height: 1,
            color: context.colorTheme.textSecondary.withOpacity(0.3),
          ),
        ),
      ],
    );
  }

  /// Builds the username register divider with text button
  Widget _buildUsernameRegisterDivider() {
    return Row(
      children: [
        Expanded(
          child: Container(
            height: 1,
            color: context.colorTheme.textSecondary.withOpacity(0.3),
          ),
        ),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.gw),
          child: GestureDetector(
            onTap: () {
              // Switch to username register
              context.read<RegisterCubit>().switchRegisterType();
            },
            child: Text(
              'Register with Username',
              style: context.textTheme.secondary.copyWith(
                color: context.colorTheme.textSecondary,
                decoration: TextDecoration.underline,
                decorationColor: context.colorTheme.textSecondary,
              ),
            ),
          ),
        ),
        Expanded(
          child: Container(
            height: 1,
            color: context.colorTheme.textSecondary.withOpacity(0.3),
          ),
        ),
      ],
    );
  }

  Widget _buildBottomSection(RegisterState state) {
    return Column(
      children: [
        _buildLoginLink(),
      ],
    );
  }

  Widget _buildRegisterButton() {
    return BlocBuilder<RegisterCubit, RegisterState>(
      builder: (context, state) {
        const isEnabled = true; // TODO: Use acceptPrivacyPolicy when available

        return CommonButton(
          title: "sign_up".tr(),
          textColor: context.colorTheme.btnTitlePrimary,
          showLoading: state.registerStatus == SimplyNetStatus.loading,
          onPressed: isEnabled
              ? () {
                  final registerType = state.registerType;

                  if ((registerType == LoginType.phone && !state.authMethodType.supportsPhone) ||
                      (registerType == LoginType.userName && !state.authMethodType.supportsAccount)) {
                    GSEasyLoading.showToast('register_method_unavailable'.tr()); // 当前注册方式不可用
                    return;
                  }

                  FocusScope.of(context).unfocus();
                  context.read<RegisterCubit>().register(loginType: registerType);
                }
              : null, // Disable button when privacy policy not accepted
        );
      },
    );
  }

  Widget _buildLoginLink() {
    return BlocBuilder<RegisterCubit, RegisterState>(
      builder: (context, state) {
        return GestureDetector(
          onTap: () {
            context.read<AuthCubit>().switchAuthType(AuthType.login);
            context.read<RegisterCubit>().switchRegisterType();
          },
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'existing_account'.tr(),
                style: context.textTheme.secondary.copyWith(
                  color: context.colorTheme.textSecondary,
                ),
              ),
              Text(
                'login_now'.tr(),
                style: context.textTheme.secondary.copyWith(
                  color: context.theme.primaryColor,
                  decoration: TextDecoration.underline,
                  decorationColor: context.theme.primaryColor,
                  decorationThickness: 1.0,
                  height: 1.5,
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
