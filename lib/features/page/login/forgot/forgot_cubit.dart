import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:wd/features/page/login/login_state.dart';

import '../../../../core/base/base_state.dart';
import '../../../../core/constants/constants.dart';
import '../../../../core/models/apis/user.dart';
import '../../../../injection_container.dart';
import '../../../../shared/widgets/easy_loading.dart';
import '../../../../shared/widgets/wangyi_captcha/wangyi_captcha.dart';
import '../../../routers/navigator_utils.dart';

part 'forgot_state.dart';

class ForgotCubit extends Cubit<ForgotState> {
  ForgotCubit()
      : super(ForgotState(
          captchaType: CaptchaType.wangYi,
          forgotType: LoginType.phone,
          username: '',
          phone: '',
          email: '',
          password: '',
          confirmPassword: '',
          smsCode: '',
          phoneController: TextEditingController(),
          emailController: TextEditingController(),
          smsCodeController: TextEditingController(),
        ));

  void setPhone(String phone) => emit(state.copyWith(phone: phone));

  void setSmsCode(String smsCode) => emit(state.copyWith(smsCode: smsCode));

  void setPassword(String password) => emit(state.copyWith(password: password));

  void setConfirmPassword(String confirmPassword) => emit(state.copyWith(confirmPassword: confirmPassword));

  void _showWangYiCaptcha({
    required String account,
    required Function(String result) onSuccess,
  }) {
    WangYiCaptcha().show(
      account: account,
      captchaId: kWangYiVerityKey,
      onSuccess: onSuccess,
      onValidateFailClose: () {
        if (!isClosed) {
          emit(state.copyWith(forgotStatus: SimplyNetStatus.idle));
        }
      },
      onError: () {
        if (!isClosed) {
          GSEasyLoading.showToast('验证失败');
          emit(state.copyWith(forgotStatus: SimplyNetStatus.failed));
        }
      },
    );
  }

  void resetPassword() async {
    sl<NavigatorService>().unFocus();

    if (state.phone.isEmpty) {
      GSEasyLoading.showToast('请输入手机号');
      return;
    }

    if (state.smsCode.isEmpty) {
      GSEasyLoading.showToast('请输入短信验证码');
      return;
    }

    if (state.password.isEmpty) {
      GSEasyLoading.showToast('请输入密码');
      return;
    }

    if (state.confirmPassword.isEmpty) {
      GSEasyLoading.showToast('请输入确认密码');
      return;
    }
    if (state.password != state.confirmPassword) {
      GSEasyLoading.showToast('两次密码输入不一致');
      return;
    }

    if (state.captchaType == CaptchaType.wangYi) {
      _showWangYiCaptcha(
        account: state.phone,
        onSuccess: (result) => fetchResetPasswordApi(),
      );
    } else {
      fetchResetPasswordApi();
    }
  }

  void fetchResetPasswordApi() async {
    try {
      GSEasyLoading.showLoading();
      emit(state.copyWith(forgotStatus: SimplyNetStatus.loading));

      final result = await UserApi.doRecoverPassword(
        phoneNo: state.phone,
        smsCode: state.smsCode,
        pwd: state.password,
        confirmPwd: state.confirmPassword,
      );
      GSEasyLoading.dismiss();
      if (result) {
        emit(state.copyWith(forgotStatus: SimplyNetStatus.success));
        GSEasyLoading.showToast('密码重置成功');
        await Future.delayed(const Duration(milliseconds: 200));
        sl<NavigatorService>().pop();
      } else {
        emit(state.copyWith(forgotStatus: SimplyNetStatus.failed));
        GSEasyLoading.showToast('密码重置失败');
      }
    } on Exception catch (_) {
      GSEasyLoading.dismiss();
    }
  }

  void updateSmsCodeController(String smsCode) => state.smsCodeController.text = smsCode;
}
