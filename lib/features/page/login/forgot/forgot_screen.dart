import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/animation/scale_animation.dart';
import 'package:wd/shared/widgets/auth/phone_prefix.dart';
import 'package:wd/shared/widgets/common_button.dart';
import 'package:wd/shared/widgets/common_textfield.dart';
import 'package:wd/shared/widgets/login/login_card_container.dart';

import '../../../../core/constants/constants.dart';
import '../../../../core/utils/system_util.dart';
import '../../../../shared/widgets/verification_code/verification_code.dart';
import 'forgot_cubit.dart';

class ForgotPage extends StatefulWidget {
  const ForgotPage({super.key});

  @override
  State<ForgotPage> createState() => _ForgotPageState();
}

class _ForgotPageState extends State<ForgotPage> {
  bool _isPasswordVisible = false;
  bool _isConfirmPasswordVisible = false;

  static const double _verticalSpacing = 15.0;
  static const double _horizontalSpacing = 10.0;
  static const double _sectionSpacing = 30.0;

  @override
  Widget build(BuildContext context) {
    return LoginCardContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(context),
          SizedBox(height: _sectionSpacing.gw),
          BlocBuilder<ForgotCubit, ForgotState>(
            builder: (context, state) {
              return CommonScaleAnimationWidget(
                children: [
                  _buildLabel(title: 'phone'.tr(), path: 'assets/images/login/phone.png'),
                  _buildPhoneInput(state),
                  SizedBox(height: _verticalSpacing.gw),
                  _buildLabel(title: '验证码', path: 'assets/images/login/shield.png'),
                  _buildVerificationCodeInput(),
                  SizedBox(height: _verticalSpacing.gw),
                  _buildLabel(title: '密码', path: 'assets/images/login/icon_login_password.png'),
                  _buildPasswordInput(
                    isVisible: _isPasswordVisible,
                    onToggleVisibility: () => setState(() => _isPasswordVisible = !_isPasswordVisible),
                    hintText: "密码6-22位字母及数字",
                    onChanged: (value) => context.read<ForgotCubit>().setPassword(value),
                  ),
                  SizedBox(height: _verticalSpacing.gw),
                  _buildLabel(title: '确认密码', path: 'assets/images/login/icon_login_password.png'),
                  _buildPasswordInput(
                    isVisible: _isConfirmPasswordVisible,
                    onToggleVisibility: () => setState(() => _isConfirmPasswordVisible = !_isConfirmPasswordVisible),
                    hintText: "请再次输入密码",
                    onChanged: (value) => context.read<ForgotCubit>().setConfirmPassword(value),
                  ),
                  SizedBox(height: _sectionSpacing.gw),
                  _buildConfirmButton(),
                  SizedBox(height: 20.gw),
                  _buildBottomText(),
                ],
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Color(0xff3B4165)),
          onPressed: () => Navigator.pop(context),
        ),
        Text(
          '找回密码',
          style: TextStyle(
            fontSize: 18.fs,
            fontWeight: FontWeight.bold,
            color: const Color(0xff3B4165),
          ),
        ),
      ],
    );
  }

  Widget _buildLabel({required String path, required String title}) {
    return Row(
      children: [
        Image.asset(
          path,
          width: 14.gw,
          height: 14.gw,
          color: const Color(0xff3B4165),
        ),
        SizedBox(width: _horizontalSpacing.gw),
        Text(
          title,
          style: TextStyle(
            fontSize: 16.fs,
            color: const Color(0xff3B4165),
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildPhoneInput(ForgotState state) {
    return CommonTextField(
      controller: state.phoneController,
      hintText: "请输入手机号",
      keyboardType: TextInputType.phone,
      isUnderline: true,
      maxLength: 11,
      suffixIconPadding: EdgeInsets.zero,
      prefixIconPadding: EdgeInsets.zero,
      prefixIcon: const PhonePrefix(),
      onChanged: (value) => context.read<ForgotCubit>().setPhone(value),
    );
  }

  Widget _buildVerificationCodeInput() {
    return BlocBuilder<ForgotCubit, ForgotState>(
      builder: (context, state) {
        return CommonTextField(
          controller: state.smsCodeController,
          hintText: "请输入验证码",
          keyboardType: TextInputType.number,
          isUnderline: true,
          onChanged: (value) => context.read<ForgotCubit>().setSmsCode(value),
          suffixIcon: VerificationCode(
            phone: state.phone,
            checkIsBind: false,
            onSmsCode: (smsCode) {
              if (kDebug && smsCode.isNotEmpty) {
                final cubit = context.read<ForgotCubit>();
                cubit.setSmsCode(smsCode);
                cubit.updateSmsCodeController(smsCode);
              }
            },
          ),
        );
      },
    );
  }

  Widget _buildPasswordInput({
    required bool isVisible,
    required VoidCallback onToggleVisibility,
    required String hintText,
    required ValueChanged<String> onChanged,
  }) {
    return CommonTextField(
      hintText: hintText,
      isUnderline: true,
      obscureText: !isVisible,
      suffixIcon: IconButton(
        icon: Icon(
          isVisible ? Icons.visibility : Icons.visibility_off,
          color: Colors.grey,
        ),
        onPressed: onToggleVisibility,
      ),
      onChanged: onChanged,
    );
  }

  Widget _buildConfirmButton() {
    return BlocSelector<ForgotCubit, ForgotState, ForgotState>(
      selector: (state) {
        return state;
      },
      builder: (context, state) {
        return CommonButton(
          title: "确认",
          textColor: const Color(0xff7E6245),
          bgImgPath: "assets/images/common/bg_btn_golden.png",
          backgroundColor: Colors.transparent,
          onPressed: () => context.read<ForgotCubit>().resetPassword(),
        );
      },
    );
  }

  Widget _buildBottomText() {
    return GestureDetector(
      onTap: () => SystemUtil.contactService(),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 20.gw),
        child: RichText(
          textAlign: TextAlign.center,
          text: TextSpan(
            style: TextStyle(
              fontSize: 12.fs,
              color: Colors.grey,
            ),
            children: [
              const TextSpan(text: '*仅支持绑定手机号的用户自助找回密码，未绑定手机号的用户请联系 '),
              TextSpan(
                text: '在线客服',
                style: TextStyle(
                  color: Theme.of(context).primaryColor,
                  decoration: TextDecoration.underline,
                ),
              ),
              const TextSpan(text: ' 找回'),
            ],
          ),
        ),
      ),
    );
  }
}
