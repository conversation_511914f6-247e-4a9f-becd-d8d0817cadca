import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:wd/core/models/apis/video.dart';
import 'package:wd/core/models/entities/user_vip_entity.dart';
import 'package:wd/core/utils/connectivity_util.dart';
import 'package:wd/core/utils/system_util.dart';

import 'video_home_state.dart';

class VideoHomeCubit extends Cubit<VideoHomeState> {
  late StreamSubscription<UserVipEntity?> _vipStream;
  late StreamSubscription<NetworkStatus> _connectivitySubscription;
  bool _initialDataFetched = false;

  List<int> watchedVideoIdList = [];

  VideoHomeCubit() : super(VideoHomeState()) {
    /// 监听网络状态
    _initializeConnectivity();
  }

  // 初始化网络监听
  void _initializeConnectivity() {
    _connectivitySubscription = ConnectivityUtil().connectivityStream.listen((status) {
      if (status.hasConnection && !_initialDataFetched) {
        _initialDataFetched = true;
        SystemUtil.checkAppUpdate();
      }

      // 可以在这里处理 WiFi 状态变化
      // 例如：在非 WiFi 环境下显示提示或调整视频质量
      _handleWifiStatusChange(status.isWifi);
    });

    // 检查当前状态
    final currentStatus = ConnectivityUtil().status;
    if (currentStatus.hasConnection && !_initialDataFetched) {
      _initialDataFetched = true;
    }
    _handleWifiStatusChange(currentStatus.isWifi);
  }

  void _handleWifiStatusChange(bool isWifi) {
    // 处理 WiFi 状态变化的逻辑
    // 例如：
    if (!isWifi) {
      // 可以发出非 WiFi 环境的提示
      // 或者调整视频质量设置等
    }
  }



  onChangePlayerStatus(HomeTikTokPlayerStatus status) {
    if (state.playerStatus != status) {
      emit(state.copyWith(playerStatus: status));
    }
  }



  /// 记录已观看的视频
  recordVideoWatched(int videoId, {required bool isNormal}) {
    if (watchedVideoIdList.contains(videoId)) return;
    watchedVideoIdList.add(videoId);
    VideoApi.submitWatchedVideoId(videoId: videoId, isNormal: isNormal);
  }

  @override
  Future<void> close() {
    _vipStream.cancel();
    _connectivitySubscription.cancel();
    return super.close();
  }
}
