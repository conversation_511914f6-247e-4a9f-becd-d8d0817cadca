// import 'dart:async';
//
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:wd/core/utils/screenUtil.dart';
// import 'package:wd/features/page/0_tiktok/video_library/video_library_view.dart';
// import 'package:wd/features/page/main/screens/main_screen_cubit.dart';
// import 'package:wd/injection_container.dart';
// import 'package:wd/shared/widgets/tiktok/home_tiktok_header.dart';
//
// import '../../../shared/widgets/notification/notification_badge.dart';
// import '../0_home/home_drawer/home_drawer_cubit.dart';
// import '../0_home/home_drawer/home_drawer_view.dart';
// import 'video_home_cubit.dart';
// import 'video_home_state.dart';
// import 'package:wd/core/utils/connectivity_util.dart';
//
// import 'video_library/popular_video/popular_video_view.dart';
//
// class VideoHomePage extends StatefulWidget {
//   const VideoHomePage({super.key});
//
//   @override
//   State<StatefulWidget> createState() => _VideoHomePageState();
// }
//
// class _VideoHomePageState extends State<VideoHomePage> with TickerProviderStateMixin {
//   final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
//
//   HomeTikTokPlayerStatus playerStatus = HomeTikTokPlayerStatus.play;
//   late AnimationController _balanceRefreshController;
//
//   final int initialPage = 1;
//   late TabController _tabController;
//   late final PageController _pageController = PageController(initialPage: initialPage);
//   late int _selectedMainTabIndex = initialPage;
//
//   int animationCount = 0;
//   bool _isDataInitialized = false;
//   late StreamSubscription<NetworkStatus> _connectivitySubscription;
//
//
//   @override
//   void initState() {
//
//     _initializeMainTabController();
//     _initConnectivityAndData();
//     showMuteToast();
//
//     _balanceRefreshController = AnimationController(
//       vsync: this,
//       duration: const Duration(seconds: 1),
//     );
//
//     // 添加监听器
//     _pageController.addListener(() {
//       double? page = _pageController.page;
//
//       // 检测整数页面变化
//       if (page != null && page % 1 == 0) {
//         _selectedMainTabIndex = page.toInt();
//         _tabController.index = _selectedMainTabIndex;
//         SystemChrome.setSystemUIOverlayStyle(
//             page == 2 || page == 3 ? SystemUiOverlayStyle.dark : SystemUiOverlayStyle.light);
//         sl<MainScreenCubit>().isTabBarWhiteColorChanged(page == 2 || page == 3);
//         sl<VideoHomeCubit>()
//             .onChangePlayerStatus(page == 2 || page == 3 ? HomeTikTokPlayerStatus.pause : HomeTikTokPlayerStatus.play);
//
//         if (mounted) {
//           setState(() {});
//         }
//       }
//     });
//     super.initState();
//   }
//
//   @override
//   void dispose() {
//     _tabController.removeListener(_handleMainTabSelection);
//     _balanceRefreshController.dispose();
//     _tabController.dispose();
//     _pageController.dispose();
//     _connectivitySubscription.cancel();
//     super.dispose();
//   }
//
//
//   void showMuteToast() async {
//     // await Future.delayed(const Duration(seconds: 10));
//     // if (GlobalConfig().isShortVideoMute) {
//     //   GSEasyLoading.showToast("当前视频处于静音状态，点击右下角音量键可恢复");
//     // }
//   }
//
//   void _initializeMainTabController() {
//     _tabController = TabController(vsync: this, length: tabList.length, initialIndex: initialPage);
//     _tabController.addListener(_handleMainTabSelection);
//     if (mounted) {
//       setState(() {});
//     }
//   }
//
//   void _handleMainTabSelection() async {
//     setState(() {
//       _selectedMainTabIndex = _tabController.index;
//       _pageController.jumpToPage(_selectedMainTabIndex);
//     });
//   }
//
//   void _initConnectivityAndData() {
//     if (ConnectivityUtil().status.hasConnection && !_isDataInitialized) {
//       _initializeData();
//     }
//
//     _connectivitySubscription = ConnectivityUtil().connectivityStream.listen((status) {
//       if (status.hasConnection && !_isDataInitialized) {
//         _initializeData();
//       }
//     });
//   }
//
//   void _initializeData() {
//     _isDataInitialized = true;
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     final cubit = BlocProvider.of<VideoHomeCubit>(context);
//     return MultiBlocListener(
//       listeners: [
//       ],
//       child: Scaffold(
//         backgroundColor: Colors.black,
//         key: _scaffoldKey,
//         drawer: BlocProvider(
//           create: (BuildContext context) => HomeDrawerCubit(),
//           child: const HomeDrawer(),
//         ),
//         body: Stack(
//           alignment: Alignment.center,
//           children: [
//             Positioned.fill(
//                 child: PageView.builder(
//                     controller: _pageController,
//                     itemCount: tabList.length,
//                     onPageChanged: (value) {
//                       if (value == 2) {
//                         setState(() {
//                           animationCount++;
//                         });
//                       }
//                     },
//                     itemBuilder: (context, index) {
//                       final currentTitle = tabList[index];
//                       if (currentTitle == "午夜影院") {
//                         // return const VideoLibraryPage();
//                       }
//                       if (currentTitle == "热门电影") {
//                         return const PopularVideoPage(
//                           animationCount: 0,
//                         );
//                       }
//                     })),
//
//             Container(
//               alignment: Alignment.topCenter,
//               padding: EdgeInsets.only(top: MediaQuery.of(context).padding.top + 20),
//               child: Row(
//                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                 children: [
//                   HomeTiktokHeader(
//                     tabs: tabList,
//                     tabController: _tabController,
//                   ),
//                   // if (_tabController.index == 3) ...[
//                   Padding(
//                     padding: EdgeInsets.only(right: 10.gw),
//                     child: InkWell(
//                       onTap: () => _scaffoldKey.currentState?.openDrawer(),
//                       child: NotificationBadge(
//                         child: Image.asset(
//                           "assets/images/home/<USER>",
//                           width: 20.gw,
//                           height: 20.gw,
//                         ),
//                       ),
//                     ),
//                   ),
//                   // ],
//                 ],
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }
