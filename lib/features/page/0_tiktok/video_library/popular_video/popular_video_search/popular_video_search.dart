import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/base/common_refresher.dart';
import 'package:wd/core/base/empty_widget.dart';
import 'package:wd/core/utils/auth_util.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/features/routers/app_router.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/tiktok/video_search_bar.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import '../../../../../../shared/widgets/tiktok/video_silver_movie_grid_view.dart';
import 'popular_video_search_cubit.dart';
import 'popular_video_search_state.dart';

class PopularVideoSearchPage extends StatefulWidget {
  const PopularVideoSearchPage({super.key});

  @override
  State<StatefulWidget> createState() => _PopularVideoSearchPageState();
}

class _PopularVideoSearchPageState extends State<PopularVideoSearchPage> {
  final textController = TextEditingController();
  final RefreshController refreshController = RefreshController(initialRefresh: false);

  void _onRefresh() {
    context.read<PopularVideoSearchCubit>().updatePageNo(1);
    context.read<PopularVideoSearchCubit>().updateIsNoMoreDataState(false);
    context.read<PopularVideoSearchCubit>().fetchVideoDataList();
  }

  void _onLoading() {
    context.read<PopularVideoSearchCubit>().updatePageNoToNext();
    context.read<PopularVideoSearchCubit>().fetchVideoDataList();
  }

  void _listener(BuildContext context, PopularVideoSearchState state) {
    refreshController.refreshCompleted();
    refreshController.loadComplete();
    if (state.isNoMoreDataState == true) {
      refreshController.loadNoData();
    }
  }

  @override
  Widget build(BuildContext context) {
    final cubit = BlocProvider.of<PopularVideoSearchCubit>(context);

    return Scaffold(
      appBar: AppBar(
        titleSpacing: 10,
        leading: IconButton(
          onPressed: () => Navigator.pop(context),
          icon: const Image(
            image: AssetImage("assets/images/toolBar/icon_toolBar_back.png"),
            height: 30,
            width: 30,
          ),
        ),
        title: _buildSearchBar(cubit),
      ),
      body: BlocConsumer<PopularVideoSearchCubit, PopularVideoSearchState>(
          listener: _listener,
          builder: (context, state) {
            if (state.isSearching) {
              return _buildSearchResults(state);
            }
            return _buildSearchHome(context, state);
          }),
    );
  }

  Widget _buildSearchBar(PopularVideoSearchCubit cubit) {
    return VideoSearchBar(
      hintText: '请输入热门影片名称',
      isShowPrefixIcon: false,
      autofocus: !cubit.state.isSearching,
      onChanged: (value) => cubit.updateSearch(value),
      onSubmitted: (value) => cubit.startSearch(value),
      onTapSearch: () => cubit.startSearch(textController.text),
      onFocusChanged: (hasFocus) {
        if (hasFocus) {
          cubit.cancelSearch();
        }
      },
      controller: textController,
    );
  }

  Widget _buildSearchHome(BuildContext context, PopularVideoSearchState state) {
    return ListView(
      children: [
        if (state.searchHistory.isNotEmpty) ...[
          _buildSectionHeader(
            '搜索历史',
            onClear: () => context.read<PopularVideoSearchCubit>().clearHistory(),
          ),
          _buildTagList(state.searchHistory),
        ],
        // _buildSectionHeader('热门搜索'),
        // _buildTagList(state.hotSearches),
      ],
    );
  }

  Widget _buildSectionHeader(String title, {VoidCallback? onClear}) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 10, 16, 0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 16.fs,
              fontWeight: FontWeight.bold,
            ),
          ),
          if (onClear != null)
            InkWell(
              onTap: onClear,
              child: Icon(Icons.clear, color: Theme.of(context).primaryColor),
            ),
        ],
      ),
    );
  }

  Widget _buildTagList(List<String> tags) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: Wrap(
        spacing: 8,
        runSpacing: 8,
        children: tags.map((tag) => _buildTag(tag)).toList(),
      ),
    );
  }

  Widget _buildTag(String tag) {
    final cubit = BlocProvider.of<PopularVideoSearchCubit>(context);
    return ActionChip(
      label: Text(tag),
      onPressed: () {
        textController.text = tag;
        cubit.startSearch(tag);
      },
    );
  }

  Widget _buildSearchResults(PopularVideoSearchState state) {
     final gridSpacing = 11.gw;
     final paddingH = 14.gw;
    // Calculate width for 3 items per row
    final gridItemWidth = (GSScreenUtil().screenWidth - paddingH * 2 - gridSpacing * 2) / 3;
    final gridItemImageHeight = 155.gw;
    final spacingBetweenText = 5.gw;
    final textAreaHeight = 60.gw;
    final gridItemHeight = gridItemImageHeight + spacingBetweenText + textAreaHeight;
    final gridItemRatio = gridItemWidth / gridItemHeight;
    if (state.netState == NetState.emptyDataState) {
      return Container(
        color: Theme.of(context).scaffoldBackgroundColor,
        child: const EmptyWidget(title: '暂无数据'),
      );
    }

    if (state.netState == NetState.loadingState) {
      return GridView.builder(
        padding: EdgeInsets.only(top: 15.gw, left: 14.gw, right: 14.gw),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          mainAxisSpacing: 10.gw,
          crossAxisSpacing: 10.gw,
          childAspectRatio: gridItemRatio,
        ),
        itemCount: 10, // 显示10个骨架项
        itemBuilder: (context, index) {
          return Container(
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 视频缩略图占位
                Expanded(
                  flex: 4,
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(8),
                        topRight: Radius.circular(8),
                      ),
                    ),
                  ),
                ),
                // 标题占位
                Expanded(
                  flex: 1,
                  child: Padding(
                    padding: EdgeInsets.all(8.gw),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          width: double.infinity,
                          height: 12.gw,
                          decoration: BoxDecoration(
                            color: Colors.grey[300],
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                        SizedBox(height: 4.gw),
                        Container(
                          width: 100.gw,
                          height: 12.gw,
                          decoration: BoxDecoration(
                            color: Colors.grey[300],
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      );
    }

    return CommonRefresher(
      bgColor: Theme.of(context).scaffoldBackgroundColor,
      enablePullDown: false,
      enablePullUp: true,
      refreshController: refreshController,
      onRefresh: null,
      onLoading: _onLoading,
      listWidget: CustomScrollView(
        slivers: [
          // 视频列表
          SliverPadding(
            padding: EdgeInsets.only(top: 15.gw, left: 14.gw, right: 14.gw),
            sliver: VideoSliverMovieGridView(
              isGrouped: false,
              hideCategory: true,
              filterVideoList: state.videoList,
              onTapCell: (videoHotMovie) {
                AuthUtil.checkIfLogin(() {
                  sl<NavigatorService>()
                      .push(AppRouter.videoDetail, arguments: {"model": videoHotMovie, "videoCategory": "popular"});
                });
              },
            ),
          ),
        ],
      ),
    );
  }
}
