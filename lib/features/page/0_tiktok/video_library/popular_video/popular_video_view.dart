import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:wd/core/base/common_refresher.dart';
import 'package:wd/core/base/empty_widget.dart';
import 'package:wd/core/base/net_error_widget.dart';
import 'package:wd/core/models/entities/video_hot_tag_entity.dart';
import 'package:wd/core/models/entities/video_list_entity.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/features/routers/app_router.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/features/page/0_tiktok/video_library/popular_video/popular_video_filter/popular_video_filter.dart';
import 'package:wd/shared/widgets/tiktok/video_search_bar.dart';
import 'package:wd/shared/widgets/tiktok/video_silver_movie_grid_view.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../../../../core/base/base_state.dart';
import '../../../../../core/models/entities/video_hot_movies_entity.dart';
import 'popular_video_filter/popular_video_filter_cubit.dart';

class PopularVideoPage extends StatefulWidget {
  const PopularVideoPage({super.key, required this.animationCount});
  final int animationCount;

  @override
  State<StatefulWidget> createState() => _PopularVideoPageState();
}

class _PopularVideoPageState extends State<PopularVideoPage> with AutomaticKeepAliveClientMixin {
  final paddingH = 10.gw;
  late final topMargin = MediaQuery.of(context).padding.top + 20 + 12.gw + 26.gw;

  late ScrollController _scrollController;
  double _opacity = 0.0; // 顶部白色渐变背景

  final RefreshController refreshController = RefreshController(initialRefresh: false);
  final TextEditingController searchController = TextEditingController();

  void _listener(BuildContext context, PopularVideoFilterState state) {
    refreshController.refreshCompleted();
    refreshController.loadComplete();
    if (state.isNoMoreDataState == true) {
      refreshController.loadNoData();
    }
  }

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _scrollController.addListener(_onScroll);
  }

  void _onLoading(bool isGrouped) {
    if (!isGrouped) {
      context.read<PopularVideoFilterCubit>().updatePageNoToNext();
    }
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    final contentY = _scrollController.offset;
    // 计算透明度，当 contentY > 10 时开始变化，最大为 1.0
    final newOpacity = (contentY - 10).clamp(0, 50) / 50.0;
    if (_opacity != newOpacity && newOpacity >= 0) {
      setState(() {
        _opacity = newOpacity;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    final cubit = context.read<PopularVideoFilterCubit>();

    return BlocConsumer<PopularVideoFilterCubit, PopularVideoFilterState>(
        listener: _listener,
        builder: (context, state) {
          if (state.hotVideosVideoNetState == NetState.errorShowRefresh) {
            return Container(
                color: Theme.of(context).scaffoldBackgroundColor,
                child: NetErrorWidget(title: '网络错误', refreshMethod: () => cubit.fetchHotVideo()));
          }
          final isGrouped = (state.categoryFilter.isEmpty || state.categoryFilter == '热门') && state.videoTitle.isEmpty;
          return Container(
            color: Theme.of(context).scaffoldBackgroundColor,
            child: Stack(
              children: [
                Container(
                  padding: EdgeInsets.symmetric(horizontal: paddingH),
                  child: CommonRefresher(
                    bgColor: Theme.of(context).scaffoldBackgroundColor,
                    enablePullDown: false,
                    enablePullUp: !isGrouped,
                    refreshController: refreshController,
                    onRefresh: null,
                    onLoading: () => _onLoading(isGrouped),
                    listWidget: CustomScrollView(
                      controller: _scrollController,
                      slivers: [
                        // 固定的空白
                        SliverToBoxAdapter(child: SizedBox(height: topMargin)),
                        // 搜索框
                        SliverToBoxAdapter(
                          child: InkWell(
                            onTap: () => sl<NavigatorService>().push(
                              AppRouter.popularVideoSearch,
                            ),
                            // onTap: () => cubit.goToSearchPage(),
                            child: VideoSearchBar(
                              enable: false,
                              controller: searchController,
                            ),
                          ),
                        ),
                        SliverToBoxAdapter(child: SizedBox(height: 15.gw)),
                        // 筛选视图
                        SliverToBoxAdapter(
                          child: AnimationLimiter(
                            key: ValueKey(widget.animationCount),
                            child: PopularVideoFilter(
                                dataList: const [],
                                moviesCategory: [
                                  VideoHotTagMoviesCategory()
                                    ..dictKey = 'all'
                                    ..dictValue = '热门',
                                  ...(state.videoHotTags?.moviesCategory ?? []),
                                ],
                                selectedItem: state.categoryFilter,
                                onFilterSelected: (item) {
                                  context.read<PopularVideoFilterCubit>().onSelectCategoryFilter(item: item);
                                }),
                          ),
                        ),

                        if (state.hotVideosVideoNetState == NetState.emptyDataState) ...[
                          SliverToBoxAdapter(
                              child: SizedBox(
                            height: 400.gw,
                            child: const EmptyWidget(title: "暂无数据"),
                          )),
                        ] else ...[
                          // 视频列表
                          SliverPadding(
                            padding: EdgeInsets.only(top: 15.gw),
                            sliver: VideoSliverMovieGridView(
                              videoList: state.hotVideoList ?? VideoHotMoviesEntity(),
                              filterVideoList: state.filterVideoList ?? [],
                              isGrouped: isGrouped,
                              onTapCell: (videoEntity) {
                                final model = VideoListRecords()
                                  ..id = videoEntity.id ?? 0
                                  ..videoImage = videoEntity.videoImage ?? ''
                                  ..videoTitle = videoEntity.videoTitle ?? ''
                                  ..videoTime = videoEntity.videoTime ?? ''
                                  ..videoYear = videoEntity.videoYear ?? ''
                                  ..videoCategory = videoEntity.videoCategory ?? ''
                                  ..videoType = videoEntity.videoType ?? 0
                                  ..videoTags = videoEntity.videoTags ?? ''
                                  ..videoCountry = videoEntity.videoCountry ?? ''
                                  ..videoClarity = videoEntity.videoClarity ?? ''
                                  ..videoBottomTag = videoEntity.videoBottomTag ?? ''
                                  ..playCount = videoEntity.playCount ?? 0
                                  ..hide = videoEntity.hide ?? 0
                                  ..createTime = videoEntity.createTime ?? '';
                                sl<NavigatorService>().push(
                                  AppRouter.videoDetail,
                                  arguments: {
                                    "model": model,
                                    "videoCategory": videoEntity.videoCategory,
                                    "pageTitle": '热门电影',
                                  },
                                );
                              },
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),

                /// 顶部AppBar白色渐变背景，随着滚动调整透明度
                Positioned(
                    top: 0,
                    left: 0,
                    child: IgnorePointer(
                      child: Container(
                        width: GSScreenUtil().screenWidth,
                        height: topMargin + 30.gw,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              Colors.white, // 动态透明度
                              Colors.white.withOpacity(_opacity), // 动态透明度
                              Colors.white.withOpacity(0.0), // 完全透明
                            ],
                            stops: const [.0, .75, 1],
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                          ),
                        ),
                      ),
                    )),
              ],
            ),
          );
        });
  }

  @override
  bool get wantKeepAlive => true;
}
