import 'package:flutter/material.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/screenUtil.dart';

class ActivityCategoryTabCell extends StatelessWidget {
  final String title;
  final int category;
  final bool isSel;

  const ActivityCategoryTabCell({
    super.key,
    required this.title,
    required this.category,
    required this.isSel,
  });

  @override
  Widget build(BuildContext context) {
    String bgImagePath = "assets/images/activity/bg_activity_tab.png";
    String iconPath = "assets/images/activity/icon_tab_${_getIconPathWithName(category)}.png";
    Color color = const Color(0xff808C9F);
    if (isSel) {
      bgImagePath = "assets/images/activity/bg_activity_tab_sel.png";
      color = const Color(0xff7E6245);
    }
    return Container(
      width: 83.gw,
      height: 42.gw,
      decoration: BoxDecoration(
        image: DecorationImage(
          image: AssetImage(bgImagePath),
          fit: BoxFit.fill,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Image.asset(iconPath, width: 26.gw, height: 26.gw, fit: BoxFit.contain, color: category == 9 ? null : color),
          SizedBox(width: 6.gw),
          Text(title, style: TextStyle(color: color, fontSize: 16.fs)),
        ],
      ),
    );
  }

  /// -1-全部 0-其他 1-新手 9-新年 10-视讯 11-捕鱼 12-棋牌 13-电子 14-彩票 15-体育
  String _getIconPathWithName(int category) {
    switch (category) {
      case -1:
        return "all";
      case 0:
        return "other";
      case 1:
        return "xr";
      case 9:
        return "new_year";
      case 10:
        return "sx";
      case 11:
        return "by";
      case 12:
        return "qp";
      case 13:
        return "dz";
      case 14:
        return "cp";
      case 15:
        return "ty";
    }
    return "all";
  }
}
