import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:wd/core/models/entities/activity_list_entity.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/features/page/2_activity/activity/activity_task_process_cell.dart';

class ActivityTaskCell extends StatelessWidget {
  final ActivityTask model;
  final VoidCallback onClickDoTask;

  /// 点击做任务
  final VoidCallback onClickCompleteTask;

  /// 点击完成任务

  const ActivityTaskCell({
    super.key,
    required this.model,
    required this.onClickDoTask,
    required this.onClickCompleteTask,
  });

  @override
  Widget build(BuildContext context) {
    Widget body = Container();
    if (model.subReceiveType < 10) {
      // 新人任务
      body = buildNewGuyInProcessContent(model);
    } else {
      body = _buildAvailableOrCompletedContent(model);
    }

    return Container(
      clipBehavior: Clip.hardEdge,
      decoration: BoxDecoration(
        // 背景渐变
        gradient: const LinearGradient(
          begin: Alignment.bottomCenter,
          end: Alignment.topCenter,
          colors: [
            Color(0xFFF4EFE9),
            Color(0xFFE1C5AE),
            Color(0xFFF4EFE9),
          ],
          stops: [0.0004, 0.5502, 1.0],
        ),
        // 边框
        border: Border.all(
          color: const Color(0xFFFFFFFF), // 边框颜色
          width: 0.73, // 边框宽度
        ),
        // 圆角
        borderRadius: BorderRadius.circular(8.gw), // 圆角半径
        // 阴影
        boxShadow: const [
          BoxShadow(
            color: Color(0x40B28338), // 阴影颜色 (40 表示 25% 透明度)
            offset: Offset(0, 2.9), // 阴影偏移
            blurRadius: 2.9, // 模糊半径
          ),
        ],
      ),
      child: body,
    );
  }

  buildNewGuyInProcessContent(ActivityTask task) {
    /// receiveStatus	领取状态 1-未完成 2-已完成未领取 3-已领取
    /// subStatus	新手活动状态 1-未开始 2-进行中 3-已结束
    /// subReceiveType 1-绑定手机号 2-绑定银行卡 3-VIP升级 4-彩票下注 5-体育下注 6-真人下注 7-电子下注 8-棋牌下注 9-捕鱼下注
    var statusImagePath = "assets/images/activity/task/icon_task_new_guys_not_start.png";
    var title = '前往完成';
    var btnBgColor = const Color(0xffB89F83);
    var isShowTagView = true;
    switch (task.subReceiveType) {
      case 1:
      case 2:
        title = "前往绑定";
      case 3:
        break;
    }
    if (task.subStatus == 2) {
      statusImagePath = "assets/images/activity/task/icon_task_new_guys_progress.png";
    }
    switch (task.receiveStatus) {
      case 2:
        title = "可领取";
        btnBgColor = const Color(0xffC0662E);
        break;
      case 3:
        title = "已领取";
        btnBgColor = const Color(0xffB5B5B5);
        isShowTagView = false;
        break;
    }
    if (task.subStatus == 3) {
      title = "已结束";
      btnBgColor = const Color(0xffB5B5B5);
      statusImagePath = "assets/images/activity/task/icon_task_new_guys_end.png";
    }

    return Stack(
      children: [
        Container(
          height: 99.gw,
          padding: EdgeInsets.only(left: 10.gw),
          child: Row(
            children: [
              Image.asset("assets/images/activity/task/icon_task_${task.subReceiveType}.png",
                  width: 40.gw, height: 40.gw),
              SizedBox(width: 8.gw),
              Expanded(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildTitle(task.title),
                    SizedBox(height: 8.gw),
                    _buildProgressBar(task.finishAmount / task.sumAmount),
                    SizedBox(height: 7.gw),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        GoldAmountTextWidget(amount: task.receiveAmount.formattedMoney),
                        Text("${task.finishAmount.formattedMoney}/${task.sumAmount.formattedMoney}"),
                      ],
                    )
                  ],
                ),
              ),
              SizedBox(width: 14.gw),
              InkWell(
                onTap: () {
                  ///receiveStatus 领取状态 1-未完成 2-已完成未领取 3-已领取
                  switch (task.receiveStatus) {
                    case 1:
                      onClickDoTask();
                      break;
                    case 2:
                      onClickCompleteTask();
                      break;
                  }
                },
                child: Container(
                  width: 26.gw,
                  height: double.infinity,
                  color: btnBgColor,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: title
                        .split('')
                        .map((str) => Text(
                              str,
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 14.fs,
                                fontWeight: FontWeight.w500,
                              ),
                            ))
                        .toList(),
                  ),
                ),
              )
            ],
          ),
        ),
        if (isShowTagView)
          Positioned(top: 0, left: 0, child: Image.asset(statusImagePath, width: 44.gw, height: 20.gw)),
      ],
    );
  }

  Widget _buildProgressBar(double progressFactor) {
    return Container(
      width: double.infinity,
      height: 8.gw,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(4.gw),
      ),
      child: FractionallySizedBox(
        alignment: Alignment.centerLeft,
        widthFactor: progressFactor,
        heightFactor: 1,
        child: Container(
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              begin: Alignment.bottomCenter,
              end: Alignment.topCenter,
              colors: [
                Color(0xFFAA8F6F),
                Color(0xFFFFECD0),
              ],
            ),
            borderRadius: BorderRadius.circular(2.gw),
          ),
        ),
      ),
    );
  }

  _buildTitle(String title) {
    return Text(
      title,
      style: TextStyle(color: const Color(0xff7E6245), fontSize: 12.fs, fontWeight: FontWeight.w600),
    );
  }

  /// 可领取/已领取
  _buildAvailableOrCompletedContent(ActivityTask task) {
    var title = '去完成';
    var btnBgColor = const Color(0xffB89F83);
    switch (task.receiveStatus) {
      case 2:
        title = "可领取";
        btnBgColor = const Color(0xffC0662E);
        break;
      case 3:
        title = "已领取";
        btnBgColor = const Color(0xffB5B5B5);
        break;
    }

    return Padding(
      padding: EdgeInsets.fromLTRB(14.gw, 8.gw, 11.gw, 10.gw),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                child: _buildTitle(task.title),
              ),
              SizedBox(width: 5.gw),
              GoldAmountTextWidget(
                amount: task.receiveAmount.formattedMoney,
              ),
            ],
          ),
          SizedBox(height: 11.gw),
          Row(
            children: [
              Expanded(
                  child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  AutoSizeText(
                    "开始时间：${task.beginTime}",
                    style: TextStyle(color: const Color(0xff7E6245), fontSize: 12.fs),
                    maxLines: 1,
                    minFontSize: 8.fs,
                  ),
                  AutoSizeText(
                    "结束时间：${task.endTime}",
                    style: TextStyle(color: const Color(0xff7E6245), fontSize: 12.fs),
                    maxLines: 1,
                    minFontSize: 8.fs,
                  ),
                ],
              )),
              SizedBox(width: 8.fs),
              InkWell(
                onTap: () {
                  if (model.receiveStatus == 1) {
                    onClickDoTask();
                  } else if (model.receiveStatus == 2) {
                    onClickCompleteTask();
                  }
                },
                child: Container(
                  height: 41.gw,
                  width: 65.gw,
                  decoration: BoxDecoration(
                    color: btnBgColor,
                    borderRadius: BorderRadius.circular(4.gw), // 圆角半径
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    title,
                    style: TextStyle(color: Colors.white, fontSize: 14.fs),
                  ),
                ),
              )
            ],
          )
        ],
      ),
    );
  }
}

class GoldAmountTextWidget extends StatelessWidget {
  final String amount;

  const GoldAmountTextWidget({super.key, required this.amount});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          height: 18.gw,
          padding: EdgeInsets.only(left: 18.gw, right: 5.gw),
          margin: EdgeInsets.only(left: 8.gw, top: 5.gw),
          decoration: BoxDecoration(
            color: const Color(0xffC8B195),
            borderRadius: BorderRadius.circular(6.gw), // 圆角半径
          ),
          child: Text(
            "彩金:$amount元",
            style: TextStyle(color: Colors.white, fontSize: 12.fs),
          ),
        ),
        Positioned(
            left: 0.gw,
            top: 0.gw,
            child: Image.asset(
              "assets/images/activity/icon_amount_gold.png",
              width: 22.gw,
              height: 23.gw,
            )),
      ],
    );
  }
}
