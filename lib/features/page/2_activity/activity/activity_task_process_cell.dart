import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:wd/core/models/entities/activity_list_entity.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:wd/core/utils/screenUtil.dart';

class ActivityTaskProcessCell extends StatelessWidget {
  final ActivityTask model;
  final VoidCallback onClickDoTask;

  /// 点击完成任务
  const ActivityTaskProcessCell({
    super.key,
    required this.model,
    required this.onClickDoTask,
  });

  @override
  Widget build(BuildContext context) {
    Widget body = Container();

    body = buildInProcessContent(model);

    return Container(
      clipBehavior: Clip.hardEdge,
      decoration: BoxDecoration(
        // 背景渐变
        gradient: const LinearGradient(
          begin: Alignment.bottomCenter,
          end: Alignment.topCenter,
          colors: [
            Color(0xFFF4EFE9),
            Color(0xFFE1C5AE),
            Color(0xFFF4EFE9),
          ],
          stops: [0.0004, 0.5502, 1.0],
        ),
        // 边框
        border: Border.all(
          color: const Color(0xFFFFFFFF), // 边框颜色
          width: 0.73, // 边框宽度
        ),
        // 圆角
        borderRadius: BorderRadius.circular(8.gw), // 圆角半径
        // 阴影
        boxShadow: const [
          BoxShadow(
            color: Color(0x40B28338), // 阴影颜色 (40 表示 25% 透明度)
            offset: Offset(0, 2.9), // 阴影偏移
            blurRadius: 2.9, // 模糊半径
          ),
        ],
      ),
      child: body,
    );
  }

  buildInProcessContent(ActivityTask task) {
    return Container(
      padding: EdgeInsets.fromLTRB(14.gw, 10.gw, 12.gw, 10.gw),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTitle(
            category: _getCategoryNameBy(task.subReceiveType),
            amount: (task.sumAmount - task.finishAmount).formattedMoney,
          ),
          SizedBox(height: 7.gw),
          Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              _buildProgressBar(task.finishAmount / task.sumAmount),
              Text("${task.finishAmount.formattedMoney}/${task.sumAmount.formattedMoney}"),
            ],
          ),
          SizedBox(height: 1.gw),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              InkWell(
                onTap: onClickDoTask,
                child: Container(
                  width: 83.gw,
                  height: 24.gw,
                  decoration: BoxDecoration(color: const Color(0xffB89F83), borderRadius: BorderRadius.circular(13.gw)),
                  child: Center(
                      child: Text(
                    '前往完成',
                    style: TextStyle(color: Colors.white, fontSize: 14.fs),
                  )),
                ),
              )
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildProgressBar(double progressFactor) {
    return Container(
      width: double.infinity,
      height: 8.gw,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(4.gw),
      ),
      child: FractionallySizedBox(
        alignment: Alignment.centerLeft,
        widthFactor: progressFactor,
        heightFactor: 1,
        child: Container(
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              begin: Alignment.bottomCenter,
              end: Alignment.topCenter,
              colors: [
                Color(0xFFAA8F6F),
                Color(0xFFFFECD0),
              ],
            ),
            borderRadius: BorderRadius.circular(2.gw),
          ),
        ),
      ),
    );
  }

  _buildTitle({String? category, String? amount}) {
    return Text(
      "完成下个$category投注任务还需要$amount投注额",
      style: TextStyle(color: const Color(0xff7E6245), fontSize: 12.fs, fontWeight: FontWeight.w600),
    );
  }

  _getCategoryNameBy(int type) {
    // 10-视讯 11-捕鱼 12-棋牌 13-电子 14-彩票 15-体育
    var name = "当前";
    switch (type) {
      case 10:
        name = "视讯";
        break;
      case 11:
        name = "捕鱼";
        break;
      case 12:
        name = "棋牌";
        break;
      case 13:
        name = "电子";
        break;
      case 14:
        name = "彩票";
        break;
      case 15:
        name = "体育";
        break;
    }
    return name;
  }
}
