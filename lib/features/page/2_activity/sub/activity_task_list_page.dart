import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/base/empty_widget.dart';
import 'package:wd/core/base/net_error_widget.dart';
import 'package:wd/core/config/bottom_nav_config.dart';
import 'package:wd/core/constants/enums.dart';
import 'package:wd/core/models/entities/activity_list_entity.dart';
import 'package:wd/core/models/view_models/activity_type.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/singletons/user_singleton.dart';
import 'package:wd/core/singletons/user_state.dart';
import 'package:wd/core/utils/global_config.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/features/page/0_tiktok/video_home_cubit.dart';
import 'package:wd/features/page/1_game_home/game_home_cubit.dart';
import 'package:wd/features/page/2_activity/activity_list_cubit.dart';
import 'package:wd/features/page/4_mine/account_security/modify_pwd/modify_pwd_view.dart';
import 'package:wd/features/page/4_mine/account_security/payment_list/payment_list_state.dart';
import 'package:wd/features/page/4_mine/mine_v2_cubit.dart';
import 'package:wd/features/page/4_mine/mine_v2_state.dart';
import 'package:wd/features/page/main/screens/main_screen_cubit.dart';
import 'package:wd/features/routers/app_router.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/activity/activity_category_tab_cell.dart';
import 'package:wd/shared/widgets/activity/activity_task_cell.dart';
import 'package:wd/shared/widgets/activity/activity_task_process_cell.dart';
import 'package:wd/shared/widgets/common_dialog.dart';
import 'package:wd/shared/widgets/easy_loading.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class ActivityTaskListPage extends StatefulWidget {
  final List<ActivityTaskTypeViewModel> dataList;
  final NetState netState;
  final int currentTabIndex;
  final GestureTapCallback refreshMethod;
  final Future Function(ActivityTaskTypeViewModel) onFetch;
  final Function(int) onChangeTabIndex;
  final Function(ActivityTask) onClickCell;

  const ActivityTaskListPage({
    super.key,
    required this.dataList,
    required this.netState,
    required this.currentTabIndex,
    required this.refreshMethod,
    required this.onFetch,
    required this.onChangeTabIndex,
    required this.onClickCell,
  });

  @override
  State<StatefulWidget> createState() => _ActivityTaskListPageState();
}

class _ActivityTaskListPageState extends State<ActivityTaskListPage> with TickerProviderStateMixin {
  final paddingH = 14.gw;
  PageController pageController = PageController();
  late AnimationController _scaleController;
  static const Duration _scaleDuration = Duration(milliseconds: 200);
  int? _animatingIndex;
  late AnimationController _slideController;
  final Map<String, RefreshController> _refreshControllers = {};

  @override
  void initState() {
    _scaleController = AnimationController(
      vsync: this,
      duration: _scaleDuration,
    );
    _slideController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    // 初始化时直接设置动画值为1.0，但是后续的tab切换会从0开始动画
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _slideController.value = 1.0;
      if (pageController.hasClients) {
        pageController.jumpToPage(widget.currentTabIndex);
      }
    });

    super.initState();
  }

  @override
  void dispose() {
    pageController.dispose();
    _scaleController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  _handleBindPhone() async {
    if (!sl<UserCubit>().state.userInfo!.hasFundPwd) {
      await CommonDialog.show(
          context: context,
          title: "提示",
          content: "请先设置资金密码",
          sureBtnTitle: "去设置",
          complete: () async {
            await sl<NavigatorService>().push(AppRouter.userModifyPwd, arguments: SetPasswordType.setFundPwd);
          });

      // If user hasn't set fund password after the dialog, return
      if (!sl<UserCubit>().state.userInfo!.hasFundPwd) return;
    }

    await sl<NavigatorService>().push(
      AppRouter.updateProfile,
      arguments: {'initialValue': sl<UserCubit>().state.userInfo?.phoneNo, 'field': UserFieldType.phone},
    );
    widget.onFetch(widget.dataList[widget.currentTabIndex]);
  }

  _handleBindBankCard() async {
    if (!sl<UserCubit>().state.userInfo!.hasFundPwd) {
      await CommonDialog.show(
          context: context,
          title: "提示",
          content: "请先设置资金密码",
          sureBtnTitle: "去设置",
          complete: () async {
            await sl<NavigatorService>().push(AppRouter.userModifyPwd, arguments: SetPasswordType.setFundPwd);
          });

      // If user hasn't set fund password after the dialog, return
      if (!sl<UserCubit>().state.userInfo!.hasFundPwd) return;
    }

    await sl<NavigatorService>().push(AppRouter.userWithdrawList, arguments: PaymentType.bankCard);
    widget.onFetch(widget.dataList[widget.currentTabIndex]);
  }

  _handleDoTask(ActivityTask model) async {
    /// receiveStatus	领取状态 1-未完成 2-已完成未领取 3-已领取
    /// subStatus	新手活动状态 1-未开始 2-进行中 3-已结束
    /// subReceiveType
    /// 主类型1( 1-绑定手机号 2-绑定银行卡 3-VIP升级 4-彩票下注 5-体育下注 6-真人下注 7-电子下注 8-棋牌下注 9-捕鱼下注)
    /// 主类型2(10-视讯 11-捕鱼 12-棋牌 13-电子 14-彩票 15-体育)
    switch (model.subReceiveType) {
      case 1: // 绑定手机号
        _handleBindPhone();
        break;
      case 2: // 绑定银行卡
        _handleBindBankCard();
        break;
      case 3: // VIP升级
        await sl<NavigatorService>().push(AppRouter.vipCenter);
        widget.onFetch(widget.dataList[widget.currentTabIndex]);
        break;
      case 4:
      case 14: // 彩票下注
    /// FIXME
    //     onSwitchToGameHomePage(GlobalConfig.getLotteryPlatformName());
        break;
      case 5:
      case 15: // 体育下注
        onSwitchToGameHomePage("体育");
        break;
      case 6:
      case 10: // 真人下注
        onSwitchToGameHomePage("视讯");
        break;
      case 7:
      case 13: // 电子下注
        onSwitchToGameHomePage("电子");
        break;
      case 8:
      case 12: // 棋牌下注
        onSwitchToGameHomePage("棋牌");
        break;
      case 9:
      case 11: // 捕鱼下注
        onSwitchToGameHomePage("捕鱼");
        break;
    }
  }

  onSwitchToGameHomePage(String tabTitle) {
    final index = context.read<GameHomeCubit>().state.gameTypeList.indexWhere((e) => e.name == tabTitle);
    if (index != -1) {
      sl<GameHomeCubit>().onChangeCurrentTabIndex(index);
    }
    sl<MainScreenCubit>().selectedNavTypeChanged(BottomNavType.gameHome);
  }

  _handleCompleteTask(ActivityTask model) {
    if (model.receiveStatus != 2) return GSEasyLoading.showToast("无法领取");
    context.read<ActivityListCubit>().onClickCompleteTask(
          id: model.id,
          category: model.subReceiveType < 10 ? 1 : model.subReceiveType,
        );
  }

  Widget _buildRightListView({
    required ActivityTaskTypeViewModel model,
    required RefreshController refreshController,
  }) {
    return BlocSelector<UserCubit, UserState, bool>(
        selector: (state) => state.isLogin,
        builder: (context, isLogin) {
          if (!isLogin) {
            return const EmptyWidget(title: "请登录后查看");
          }

          if (model.netState == NetState.errorShowRefresh) {
            return NetErrorWidget(
              title: "获取失败，点击重试",
              refreshMethod: () => widget.onFetch(model),
            );
          }

          if (model.netState == NetState.emptyDataState) {
            return const EmptyWidget(title: "暂无活动");
          }

          final body = AnimationLimiter(
            key: ValueKey('task_page_${model.categoryName}'),
            child: ListView.separated(
              padding: EdgeInsets.only(right: paddingH, top: 5.gw, bottom: 10.gw),
              itemCount: model.list.length,
              itemBuilder: (BuildContext context, int index) {
                final task = model.list[index];

                /// 进度条
                if (task.isProcess) {
                  return _animatedCell(
                    index: index,
                    child: ActivityTaskProcessCell(
                      model: task,
                      onClickDoTask: () => _handleDoTask(task),
                    ),
                  );
                }

                /// 任务
                return _animatedCell(
                  index: index,
                  child: ActivityTaskCell(
                    model: task,
                    onClickDoTask: () => _handleDoTask(task),
                    onClickCompleteTask: () => _handleCompleteTask(task),
                  ),
                );
              },
              separatorBuilder: (context, index) => SizedBox(height: 10.gw),
            ),
          );

          return SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(0, 0.15),
              end: Offset.zero,
            ).animate(CurvedAnimation(
              parent: _slideController,
              curve: Curves.easeOut,
            )),
            child: body,
          );
        });
  }

  _animatedCell({required Widget child, required int index}) {
    return AnimationConfiguration.staggeredList(
      position: index,
      duration: const Duration(milliseconds: 375),
      child: SlideAnimation(
        verticalOffset: 50.0,
        child: FadeInAnimation(
          child: child,
        ),
      ),
    );
  }

  Widget _buildLeftTabList() {
    final List<ActivityTaskTypeViewModel> data = widget.dataList.where((element) => element.category != 9).toList();
    return SizedBox(
      width: 83.gw + paddingH + 12.gw,
      child: ListView.separated(
        padding: EdgeInsets.only(left: paddingH, right: 12.gw, top: 5.gw, bottom: 10.gw),
        itemBuilder: (context, index) => _buildTabItem(index, data),
        separatorBuilder: (_, __) => SizedBox(height: 10.gw),
        itemCount: data.length,
      ),
    );
  }

  Widget _buildTabItem(int index, List<ActivityTaskTypeViewModel> data) {
    final model = data[index];
    final isSelected = widget.currentTabIndex == index;
    Widget cell = ActivityCategoryTabCell(
      title: model.categoryName,
      category: model.category,
      isSel: isSelected,
    );

    if (_animatingIndex == index) {
      cell = _wrapWithScaleAnimation(cell);
    }

    return InkWell(
      onTap: () => _handleTabTap(index, model),
      child: cell,
    );
  }

  Widget _buildRightPageView() {
    final List<ActivityTaskTypeViewModel> data = widget.dataList.where((element) => element.category != 9).toList();

    return Expanded(
      child: PageView.builder(
        controller: pageController,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: data.length,
        itemBuilder: (_, index) {
          final model = data[index];
          final refreshController = _refreshControllers.putIfAbsent(
            model.categoryName,
            () => RefreshController(initialRefresh: false),
          );

          return _buildRightListView(model: model, refreshController: refreshController);
        },
      ),
    );
  }

  void _handleTabTap(int index, ActivityTaskTypeViewModel model) {
    if (widget.currentTabIndex == index) return;

    setState(() {
      _animatingIndex = index;
    });

    widget.onChangeTabIndex(index);
    widget.onFetch(model);

    pageController.jumpToPage(index);
    _slideController.reset();
    _slideController.forward();

    _scaleController.forward().then((_) {
      _scaleController.reverse().then((_) {
        setState(() => _animatingIndex = null);
      });
    });
  }

  Widget _wrapWithScaleAnimation(Widget child) {
    return ScaleTransition(
      scale: Tween<double>(begin: 1.0, end: 1.1).animate(
        CurvedAnimation(
          parent: _scaleController,
          curve: Curves.easeInOut,
        ),
      ),
      child: child,
    );
  }

  @override
  Widget build(BuildContext context) {
    if (widget.dataList.isEmpty) {
      if (widget.netState == NetState.errorShowRefresh) {
        return NetErrorWidget(refreshMethod: widget.refreshMethod);
      }

      return Center(
        child: SizedBox(
          width: 25.gw,
          height: 25.gw,
          child: const CircularProgressIndicator(),
        ),
      );
    }

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildLeftTabList(),
        _buildRightPageView(),
      ],
    );
  }
}
