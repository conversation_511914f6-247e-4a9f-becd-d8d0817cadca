part of 'activity_list_cubit.dart';

class ActivityListState extends BaseState {
  /// 游戏活动、自助领取的tabBar
  int currentTabIndex = 0; 
  /// 今天星期几
  int currentDayId = 1; 
  /// 今天是否已签到
  bool isCheckedToday = false; 
  /// 签到天数
  int totalSignedDays = 0; 
  /// 累计奖励
  double totalGoldEarned = 0; 
  /// 连续签到天数
  int consecutiveSignedDays = 0; 
  DailyCheckInEntity? checkInModel;
  NetState checkInListNetState = NetState.loadingState;

  List<ActivityGameTypeViewModel> gameCategoryList = [];
  NetState gameNetState = NetState.loadingState;
  int currentGameTabIndex = 0;

  List<ActivityTaskTypeViewModel> taskCategoryList = [];
  NetState taskNetState = NetState.loadingState;
  int currentTaskTabIndex = 0;

  ActivityListState init() {
    return ActivityListState()
      ..currentTabIndex = 0
      ..currentDayId = 1
      ..totalSignedDays = 1
      ..totalGoldEarned = 1
      ..consecutiveSignedDays = 1
      ..isNoMoreDataState = false
      ..isCheckedToday = false
      ..checkInModel = null
      ..gameCategoryList = []
      ..currentGameTabIndex = 0
      ..taskCategoryList = []
      ..currentTaskTabIndex = 0
      ..checkInListNetState = NetState.loadingState
      ..gameNetState = NetState.loadingState
      ..taskNetState = NetState.loadingState
      ..currentTaskTabIndex = 0
      ..dataList = [];
  }

  ActivityListState clone() {
    return ActivityListState()
      ..currentTabIndex = currentTabIndex
      ..currentDayId = currentDayId
      ..totalSignedDays = totalSignedDays
      ..totalGoldEarned = totalGoldEarned
      ..consecutiveSignedDays = consecutiveSignedDays
      ..dataList = dataList
      ..isCheckedToday = isCheckedToday
      ..isNoMoreDataState = isNoMoreDataState
      ..checkInModel = checkInModel
      ..gameCategoryList = gameCategoryList
      ..currentGameTabIndex = currentGameTabIndex
      ..taskCategoryList = taskCategoryList
      ..currentTaskTabIndex = currentTaskTabIndex
      ..checkInListNetState = checkInListNetState
      ..gameNetState = gameNetState
      ..taskNetState = taskNetState
      ..netState = netState;
  }

  List<Object?> get props => [
        checkInModel,
      ];
}
