import 'package:flutter/material.dart';

import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/chat/color_pallette.dart';
import 'package:wd/core/utils/theme/theme_cubit.dart';
import 'package:wd/core/utils/chat/utils.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/features/page/3_chat_new/screens/chat_screen.dart';
import 'package:wd/features/page/3_chat_new/widgets/chat/dp.dart';

class SpecialTile extends StatelessWidget {
  const SpecialTile({
    super.key,
    this.name,
    this.url,
    this.groupId,
    this.userId,
  });

  final String? name;
  final String? url;
  final String? groupId;
  final String? userId;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        if (userId != null) {
          Navigator.push<dynamic>(
            context,
            MaterialPageRoute(
              builder: (context) => ChatScreen(
                selectedConversation: getConversation(
                  userID: userId,
                  name: name,
                ),
              ),
            ),
          );
        }
        if (groupId != null) {
          Navigator.push<dynamic>(
            context,
            MaterialPageRoute(
              builder: (context) => ChatScreen(
                selectedConversation: getConversation(
                  groupID: groupId,
                  name: name,
                ),
              ),
            ),
          );
        }
      },
      child: Container(
        // height: 58.gw,
        padding: const EdgeInsets.fromLTRB(16, 6, 12, 6),
        child: Column(
          children: [
            Row(
              children: [
                Dp(
                  faceUrl: url,
                  type: groupId != null ? 2 : 1,
                  userId: userId ?? groupId,
                  name: name,
                ),
                Expanded(
                  child: Container(
                    alignment: Alignment.centerLeft,
                    padding: const EdgeInsets.only(right: 28, left: 12),
                    child: Text(
                      name ?? '',
                      style: TextStyle(
                        color: context.colorTheme.textRegular,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ],
            ),
            Padding(
              padding: const EdgeInsets.only(top: 5),
              child: Divider(
                thickness: 0.35,
                height: 0,
                indent: 60,
                endIndent: 15,
                color: ColorPalette.greyColor2,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
