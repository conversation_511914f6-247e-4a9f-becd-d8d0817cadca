import 'package:wd/generated/json/base/json_convert_content.dart';
import 'package:wd/core/models/entities/system_config_entity.dart';
import 'package:wd/core/constants/constants.dart';


SystemConfigEntity $SystemConfigEntityFromJson(Map<String, dynamic> json) {
  final SystemConfigEntity systemConfigEntity = SystemConfigEntity();
  final String? inviteDomain = jsonConvert.convert<String>(json['invite_domain']);
  if (inviteDomain != null) {
    systemConfigEntity.inviteDomain = inviteDomain;
  }
  final String? loginCaptcha = jsonConvert.convert<String>(json['login_captcha']);
  if (loginCaptcha != null) {
    systemConfigEntity.loginCaptcha = loginCaptcha;
  }
  final String? registerCaptcha = jsonConvert.convert<String>(json['register_captcha']);
  if (registerCaptcha != null) {
    systemConfigEntity.registerCaptcha = registerCaptcha;
  }
  final String? chatDomain = jsonConvert.convert<String>(json['chat_domain']);
  if (chatDomain != null) {
    systemConfigEntity.chatDomain = chatDomain;
  }
  final String? videoDomain = jsonConvert.convert<String>(json['video_domain']);
  if (videoDomain != null) {
    systemConfigEntity.videoDomain = videoDomain;
  }
  final String? serviceUrl = jsonConvert.convert<String>(json['service_url']);
  if (serviceUrl != null) {
    systemConfigEntity.serviceUrl = serviceUrl;
  }
  final String? appDownloadUrl = jsonConvert.convert<String>(json['app_download_url']);
  if (appDownloadUrl != null) {
    systemConfigEntity.appDownloadUrl = appDownloadUrl;
  }
  final String? serviceOpenType = jsonConvert.convert<String>(json['service_open_type']);
  if (serviceOpenType != null) {
    systemConfigEntity.serviceOpenType = serviceOpenType;
  }
  final String? videoWatchLimit = jsonConvert.convert<String>(json['video_watch_limit']);
  if (videoWatchLimit != null) {
    systemConfigEntity.videoWatchLimit = videoWatchLimit;
  }
  final String? videoWatchRule = jsonConvert.convert<String>(json['video_watch_rule']);
  if (videoWatchRule != null) {
    systemConfigEntity.videoWatchRule = videoWatchRule;
  }
  final String? gamePicBaseUrl = jsonConvert.convert<String>(json['aws_access_domain']);
  if (gamePicBaseUrl != null) {
    systemConfigEntity.gamePicBaseUrl = gamePicBaseUrl;
  }
  final String? currencyCode = jsonConvert.convert<String>(json['currency_code']);
  if (currencyCode != null) {
    systemConfigEntity.currencyCode = currencyCode;
  }
  final String? currencySymbol = jsonConvert.convert<String>(json['currency_symbol']);
  if (currencySymbol != null) {
    systemConfigEntity.currencySymbol = currencySymbol;
  }
  final String? wtdCheckPhone = jsonConvert.convert<String>(json['wtdCheckPhone']);
  if (wtdCheckPhone != null) {
    systemConfigEntity.wtdCheckPhone = wtdCheckPhone;
  }
  final LanguageType? languageType = jsonConvert.convert<LanguageType>(json['languageType']);
  if (languageType != null) {
    systemConfigEntity.languageType = languageType;
  }
  final LoadLoginAndRegWay? loadLoginAndRegWay = jsonConvert.convert<LoadLoginAndRegWay>(json['loadLoginAndRegWay']);
  if (loadLoginAndRegWay != null) {
    systemConfigEntity.loadLoginAndRegWay = loadLoginAndRegWay;
  }
  return systemConfigEntity;
}

Map<String, dynamic> $SystemConfigEntityToJson(SystemConfigEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['invite_domain'] = entity.inviteDomain;
  data['login_captcha'] = entity.loginCaptcha;
  data['register_captcha'] = entity.registerCaptcha;
  data['chat_domain'] = entity.chatDomain;
  data['video_domain'] = entity.videoDomain;
  data['service_url'] = entity.serviceUrl;
  data['app_download_url'] = entity.appDownloadUrl;
  data['service_open_type'] = entity.serviceOpenType;
  data['video_watch_limit'] = entity.videoWatchLimit;
  data['video_watch_rule'] = entity.videoWatchRule;
  data['aws_access_domain'] = entity.gamePicBaseUrl;
  data['currency_code'] = entity.currencyCode;
  data['currency_symbol'] = entity.currencySymbol;
  data['wtdCheckPhone'] = entity.wtdCheckPhone;
  data['languageType'] = entity.languageType.toJson();
  data['loadLoginAndRegWay'] = entity.loadLoginAndRegWay.toJson();
  return data;
}

extension SystemConfigEntityExtension on SystemConfigEntity {
  SystemConfigEntity copyWith({
    String? inviteDomain,
    String? loginCaptcha,
    String? registerCaptcha,
    String? chatDomain,
    String? videoDomain,
    String? serviceUrl,
    String? appDownloadUrl,
    String? serviceOpenType,
    String? videoWatchLimit,
    String? videoWatchRule,
    String? gamePicBaseUrl,
    String? currencyCode,
    String? currencySymbol,
    String? wtdCheckPhone,
    LanguageType? languageType,
    LoadLoginAndRegWay? loadLoginAndRegWay,
  }) {
    return SystemConfigEntity()
      ..inviteDomain = inviteDomain ?? this.inviteDomain
      ..loginCaptcha = loginCaptcha ?? this.loginCaptcha
      ..registerCaptcha = registerCaptcha ?? this.registerCaptcha
      ..chatDomain = chatDomain ?? this.chatDomain
      ..videoDomain = videoDomain ?? this.videoDomain
      ..serviceUrl = serviceUrl ?? this.serviceUrl
      ..appDownloadUrl = appDownloadUrl ?? this.appDownloadUrl
      ..serviceOpenType = serviceOpenType ?? this.serviceOpenType
      ..videoWatchLimit = videoWatchLimit ?? this.videoWatchLimit
      ..videoWatchRule = videoWatchRule ?? this.videoWatchRule
      ..gamePicBaseUrl = gamePicBaseUrl ?? this.gamePicBaseUrl
      ..currencyCode = currencyCode ?? this.currencyCode
      ..currencySymbol = currencySymbol ?? this.currencySymbol
      ..wtdCheckPhone = wtdCheckPhone ?? this.wtdCheckPhone
      ..languageType = languageType ?? this.languageType
      ..loadLoginAndRegWay = loadLoginAndRegWay ?? this.loadLoginAndRegWay;
  }
}

LoadLoginAndRegWay $LoadLoginAndRegWayFromJson(Map<String, dynamic> json) {
  final LoadLoginAndRegWay loadLoginAndRegWay = LoadLoginAndRegWay();
  final String? login = jsonConvert.convert<String>(json['login']);
  if (login != null) {
    loadLoginAndRegWay.login = login;
  }
  final String? register = jsonConvert.convert<String>(json['register']);
  if (register != null) {
    loadLoginAndRegWay.register = register;
  }
  final String? defLogin = jsonConvert.convert<String>(json['defLogin']);
  if (defLogin != null) {
    loadLoginAndRegWay.defLogin = defLogin;
  }
  final String? defRegister = jsonConvert.convert<String>(json['defRegister']);
  if (defRegister != null) {
    loadLoginAndRegWay.defRegister = defRegister;
  }
  return loadLoginAndRegWay;
}

Map<String, dynamic> $LoadLoginAndRegWayToJson(LoadLoginAndRegWay entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['login'] = entity.login;
  data['register'] = entity.register;
  data['defLogin'] = entity.defLogin;
  data['defRegister'] = entity.defRegister;
  return data;
}

extension LoadLoginAndRegWayExtension on LoadLoginAndRegWay {
  LoadLoginAndRegWay copyWith({
    String? login,
    String? register,
    String? defLogin,
    String? defRegister,
  }) {
    return LoadLoginAndRegWay()
      ..login = login ?? this.login
      ..register = register ?? this.register
      ..defLogin = defLogin ?? this.defLogin
      ..defRegister = defRegister ?? this.defRegister;
  }
}

LanguageType $LanguageTypeFromJson(Map<String, dynamic> json) {
  final LanguageType languageType = LanguageType();
  final String? defaultLanguage = jsonConvert.convert<String>(json['defaultLanguage']);
  if (defaultLanguage != null) {
    languageType.defaultLanguage = defaultLanguage;
  }
  final List<LanguageConfig>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<LanguageConfig>(e) as LanguageConfig).toList();
  if (list != null) {
    languageType.list = list;
  }
  return languageType;
}

Map<String, dynamic> $LanguageTypeToJson(LanguageType entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['defaultLanguage'] = entity.defaultLanguage;
  data['list'] = entity.list.map((v) => v.toJson()).toList();
  return data;
}

extension LanguageTypeExtension on LanguageType {
  LanguageType copyWith({
    String? defaultLanguage,
    List<LanguageConfig>? list,
  }) {
    return LanguageType()
      ..defaultLanguage = defaultLanguage ?? this.defaultLanguage
      ..list = list ?? this.list;
  }
}

LanguageConfig $LanguageConfigFromJson(Map<String, dynamic> json) {
  final LanguageConfig languageConfig = LanguageConfig();
  final String? dictLabel = jsonConvert.convert<String>(json['dictLabel']);
  if (dictLabel != null) {
    languageConfig.dictLabel = dictLabel;
  }
  final String? dictValue = jsonConvert.convert<String>(json['dictValue']);
  if (dictValue != null) {
    languageConfig.dictValue = dictValue;
  }
  final int? dictSort = jsonConvert.convert<int>(json['dictSort']);
  if (dictSort != null) {
    languageConfig.dictSort = dictSort;
  }
  return languageConfig;
}

Map<String, dynamic> $LanguageConfigToJson(LanguageConfig entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['dictLabel'] = entity.dictLabel;
  data['dictValue'] = entity.dictValue;
  data['dictSort'] = entity.dictSort;
  return data;
}

extension LanguageConfigExtension on LanguageConfig {
  LanguageConfig copyWith({
    String? dictLabel,
    String? dictValue,
    int? dictSort,
  }) {
    return LanguageConfig()
      ..dictLabel = dictLabel ?? this.dictLabel
      ..dictValue = dictValue ?? this.dictValue
      ..dictSort = dictSort ?? this.dictSort;
  }
}