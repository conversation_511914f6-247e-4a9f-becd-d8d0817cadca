import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:wd/features/page/4_mine/profile/widgets/profile_date_picker.dart';
import 'package:wd/features/page/4_mine/profile/profile_cubit.dart';

void main() {
  group('ProfileDatePicker Tests', () {
    late UserProfileCubit mockCubit;

    setUp(() {
      mockCubit = UserProfileCubit();
    });

    testWidgets('CustomDatePickerContent should build correctly', (WidgetTester tester) async {
      final now = DateTime.now();
      
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<UserProfileCubit>(
            create: (context) => mockCubit,
            child: Scaffold(
              body: CustomDatePickerContent(now: now),
            ),
          ),
        ),
      );

      // Verify the widget builds without errors
      expect(find.byType(CustomDatePickerContent), findsOneWidget);
      
      // Verify the three columns are present (day, month, year)
      expect(find.byType(Row), findsOneWidget);
      expect(find.byType(Expanded), findsNWidgets(3));
    });

    testWidgets('Date picker should handle date changes correctly', (WidgetTester tester) async {
      final now = DateTime(2024, 8, 15);
      
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<UserProfileCubit>(
            create: (context) => mockCubit,
            child: Scaffold(
              body: CustomDatePickerContent(now: now),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // The widget should initialize with current date
      expect(find.byType(CustomDatePickerContent), findsOneWidget);
    });

    test('_getDaysInMonth should return correct number of days', () {
      // Test regular month
      expect(_testGetDaysInMonth(2024, 1), 31); // January
      expect(_testGetDaysInMonth(2024, 4), 30); // April
      expect(_testGetDaysInMonth(2024, 2), 29); // February 2024 (leap year)
      expect(_testGetDaysInMonth(2023, 2), 28); // February 2023 (non-leap year)
    });
  });
}

// Helper function to test the private _getDaysInMonth method
int _testGetDaysInMonth(int year, int month) {
  return DateTime(year, month + 1, 0).day;
}
