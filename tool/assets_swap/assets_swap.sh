# 多渠道替换flutter静态资源，用des替换至src
channel="_$1"
echo ">>>>>正在替换渠道资源"
if [ ! -d "assets/${channel}" ]; then
  echo ">>>>>替换失败，请检查assets/${channel}，目录是否存在"
else
  cp -Rf assets/${channel}/images/. assets/images
  cp -Rf assets/${channel}/html/. assets/html
  cp -Rf assets/${channel}/json/. assets/json
  cp -Rf assets/${channel}/video/. assets/video
  cp -Rf assets/${channel}/web/. web
  echo ">>>>>成功替换资源，当前渠道资源：${channel}"
fi

echo ">>>>>正在执行启动页资源替换"

if dart run flutter_native_splash:create --path=flavor/$1_splash.yaml; then
  echo "✅ >>>>>成功启动页资源替换"
else
  echo "❌ >>>>>启动页资源替换失败"
  exit 1
fi