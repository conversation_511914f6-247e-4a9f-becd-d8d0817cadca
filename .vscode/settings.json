{"cSpell.words": ["Akzidenz", "Grotesk", "Lucide", "watchlist"], "dart.previewFlutterUiGuides": true, "dart.previewFlutterUiGuidesCustomTracking": true, "dart.showInspectorNotificationsForWidgetErrors": true, "editor.bracketPairColorization.enabled": true, "editor.formatOnSave": true, "editor.formatOnType": true, "editor.guides.bracketPairs": true, "dart.lineLength": 120, "files.exclude": {"**/.DS_Store": true, "**/.git": true, "**/.githooks": false, "**/*.freezed.dart": true, "**/*.g.dart": true, "**/*.gr.dart": true, "**/*.mocks.dart": true}, "search.exclude": {"**/.dart_tool": true, "**/.fvm": true, "**/.idea": true, "**/*.freezed.dart": true, "**/*.g.dart": true, "**/*.gr.dart": true, "**/*.mocks.dart": true, "**/build": true, "**/ios/Pods": true}, "dart.flutterSdkPath": ".fvm/versions/3.29.2"}